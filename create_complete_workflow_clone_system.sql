-- Complete Workflow Clone System
-- Creates clone tables for workflows, workflow_versions, and workflow_marketplace_listings
-- with proper foreign key relationships pointing to clone tables

-- =====================================================
-- 1. DROP EXISTING CLONE TABLES (if they exist)
-- =====================================================

DROP TABLE IF EXISTS workflow_marketplace_listings_clone CASCADE;
DROP TABLE IF EXISTS workflow_versions_clone CASCADE;
-- workflows_clone already exists, so we'll keep it

-- =====================================================
-- 2. CREATE WORKFLOW_VERSIONS_CLONE TABLE
-- =====================================================

CREATE TABLE workflow_versions_clone (
    id VARCHAR PRIMARY KEY,
    workflow_id VARCHAR NOT NULL,
    version_number VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR,
    workflow_url VARCHAR NOT NULL,
    builder_url VARCHAR NOT NULL,
    start_nodes JSONB NOT NULL DEFAULT '[]',
    available_nodes JSONB NOT NULL DEFAULT '[]',
    category VARCHAR,  -- Will be enum in application
    tags JSONB DEFAULT '[]',
    changelog TEXT,
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',  -- Will be enum in application
    is_customizable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Foreign key to workflows_clone
    CONSTRAINT workflow_versions_clone_workflow_id_fkey 
        FOREIGN KEY (workflow_id) REFERENCES workflows_clone(id) ON DELETE CASCADE
);

-- =====================================================
-- 3. CREATE WORKFLOW_MARKETPLACE_LISTINGS_CLONE TABLE
-- =====================================================

CREATE TABLE workflow_marketplace_listings_clone (
    id VARCHAR PRIMARY KEY,
    workflow_version_id VARCHAR NOT NULL,
    workflow_id VARCHAR NOT NULL,
    listed_by_user_id VARCHAR NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR,
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    tags JSONB DEFAULT '[]',
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',  -- Will be enum in application
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Foreign keys to clone tables
    CONSTRAINT workflow_marketplace_listings_clone_version_id_fkey 
        FOREIGN KEY (workflow_version_id) REFERENCES workflow_versions_clone(id) ON DELETE CASCADE,
    CONSTRAINT workflow_marketplace_listings_clone_workflow_id_fkey 
        FOREIGN KEY (workflow_id) REFERENCES workflows_clone(id) ON DELETE CASCADE
);

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for workflow_versions_clone
CREATE INDEX idx_workflow_versions_clone_workflow_id ON workflow_versions_clone(workflow_id);
CREATE INDEX idx_workflow_versions_clone_status ON workflow_versions_clone(status);
CREATE INDEX idx_workflow_versions_clone_created_at ON workflow_versions_clone(created_at);
CREATE INDEX idx_workflow_versions_clone_category ON workflow_versions_clone(category);

-- Indexes for workflow_marketplace_listings_clone
CREATE INDEX idx_workflow_marketplace_listings_clone_workflow_id ON workflow_marketplace_listings_clone(workflow_id);
CREATE INDEX idx_workflow_marketplace_listings_clone_version_id ON workflow_marketplace_listings_clone(workflow_version_id);
CREATE INDEX idx_workflow_marketplace_listings_clone_status ON workflow_marketplace_listings_clone(status);
CREATE INDEX idx_workflow_marketplace_listings_clone_created_at ON workflow_marketplace_listings_clone(created_at);

-- =====================================================
-- 5. INSERT SAMPLE DATA FOR WORKFLOW_VERSIONS_CLONE
-- =====================================================

-- Create sample versions for our existing workflows
INSERT INTO workflow_versions_clone (
    id, workflow_id, version_number, name, description, image_url,
    workflow_url, builder_url, start_nodes, available_nodes,
    category, tags, changelog, status, is_customizable, created_at
) 
SELECT 
    CONCAT(id, '-v1') as id,
    id as workflow_id,
    '1.0.0' as version_number,
    name,
    description,
    image_url,
    workflow_url,
    builder_url,
    start_nodes,
    available_nodes,
    category,
    tags,
    'Initial version' as changelog,
    status,
    is_customizable,
    created_at
FROM workflows_clone;

-- =====================================================
-- 6. INSERT SAMPLE DATA FOR MARKETPLACE_LISTINGS_CLONE
-- =====================================================

-- Create sample marketplace listings for public workflows
INSERT INTO workflow_marketplace_listings_clone (
    id, workflow_version_id, workflow_id, listed_by_user_id,
    title, description, image_url, price, currency, tags,
    status, created_at, updated_at
)
SELECT 
    CONCAT(wc.id, '-listing') as id,
    CONCAT(wc.id, '-v1') as workflow_version_id,
    wc.id as workflow_id,
    wc.owner_id as listed_by_user_id,
    CONCAT(wc.name, ' - Marketplace Listing') as title,
    CONCAT('Marketplace listing for: ', wc.description) as description,
    wc.image_url,
    CASE 
        WHEN wc.env_credential_status = 'not_required' THEN 0.00
        ELSE 9.99
    END as price,
    'USD' as currency,
    wc.tags,
    'ACTIVE' as status,
    wc.created_at,
    wc.updated_at
FROM workflows_clone wc
WHERE wc.visibility = 'PUBLIC' 
AND wc.status = 'ACTIVE';

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Count records in each clone table
SELECT 'workflows_clone' as table_name, COUNT(*) as count FROM workflows_clone
UNION ALL
SELECT 'workflow_versions_clone' as table_name, COUNT(*) as count FROM workflow_versions_clone
UNION ALL
SELECT 'workflow_marketplace_listings_clone' as table_name, COUNT(*) as count FROM workflow_marketplace_listings_clone;

-- Verify foreign key relationships
SELECT 
    wc.id as workflow_id,
    wc.name as workflow_name,
    wvc.id as version_id,
    wvc.version_number,
    wmlc.id as listing_id,
    wmlc.title as listing_title
FROM workflows_clone wc
LEFT JOIN workflow_versions_clone wvc ON wc.id = wvc.workflow_id
LEFT JOIN workflow_marketplace_listings_clone wmlc ON wc.id = wmlc.workflow_id
ORDER BY wc.created_at
LIMIT 5;

-- Success message
SELECT 'Successfully created complete workflow clone system with all related tables!' as status;
