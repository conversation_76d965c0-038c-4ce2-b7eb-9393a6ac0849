#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a clone of the workflows table with 10 sample rows.
This script reads the SQL file and executes it against the PostgreSQL database.

Usage:
    Run from workflow-service directory:
    poetry run python ../create_clone_table.py

Requirements:
    - PostgreSQL database running
    - Run from workflow-service directory with poetry environment
"""

import os
import sys
from pathlib import Path

# Add the workflow-service app to the path
sys.path.append(os.path.join(os.path.dirname(__file__), "workflow-service"))

try:
    from app.core.config import settings
    from app.db.session import engine
    from sqlalchemy import text
    from sqlalchemy.exc import SQLAlchemyError
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run this script from the workflow-service directory using:")
    print("poetry run python ../create_clone_table.py")
    sys.exit(1)


def get_database_url():
    """Get database URL from workflow-service settings"""
    return str(settings.SQLALCHEMY_DATABASE_URI)


def read_sql_file(file_path):
    """Read the SQL file content"""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return file.read()
    except FileNotFoundError:
        raise FileNotFoundError(f"SQL file not found: {file_path}")
    except Exception as e:
        raise Exception(f"Error reading SQL file: {e}")


def execute_sql_script(engine, sql_content):
    """Execute the SQL script"""
    try:
        with engine.connect() as connection:
            # Split the SQL content by semicolons to execute individual statements
            statements = [
                stmt.strip() for stmt in sql_content.split(";") if stmt.strip()
            ]

            results = []
            for i, statement in enumerate(statements):
                if statement.upper().startswith("SELECT"):
                    # Execute SELECT statements and fetch results
                    result = connection.execute(text(statement))
                    rows = result.fetchall()
                    if rows:
                        print(f"\n--- Query {i+1} Results ---")
                        for row in rows:
                            print(row)
                        results.append(rows)
                else:
                    # Execute DDL/DML statements
                    connection.execute(text(statement))
                    connection.commit()
                    print(f"✅ Executed statement {i+1}: {statement[:50]}...")

            return results

    except SQLAlchemyError as e:
        raise Exception(f"Database error: {e}")
    except Exception as e:
        raise Exception(f"Error executing SQL: {e}")


def main():
    """Main function to create the clone table"""
    print("🚀 Creating workflows clone table with 10 sample rows...")

    try:
        # Get database URL
        db_url = get_database_url()
        print(f"📊 Using database: {db_url}")

        # Test connection using existing engine
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ Database connection successful!")

        # Read SQL file
        sql_file_path = Path(__file__).parent / "clone_workflows_table.sql"
        print(f"📄 Reading SQL file: {sql_file_path}")
        sql_content = read_sql_file(sql_file_path)

        # Execute SQL script
        print("⚡ Executing SQL script...")
        results = execute_sql_script(engine, sql_content)

        print("\n🎉 Successfully created workflows_clone table!")
        print("📋 Summary:")
        print("   - Table: workflows_clone")
        print("   - Rows: 10 sample workflows")
        print("   - Includes: Marketplace authentication fields")
        print("   - Indexes: Created for performance")

        print("\n💡 You can now use this table for testing:")
        print("   SELECT * FROM workflows_clone;")
        print("   SELECT COUNT(*) FROM workflows_clone;")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
