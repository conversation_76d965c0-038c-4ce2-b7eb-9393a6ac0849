# Workflows Table Clone with Sample Data

This directory contains scripts to create a clone of the workflows database table with only 10 rows of sample data, perfect for testing and development without the overhead of a large production dataset.

## 📁 Files

- **`clone_workflows_table.sql`** - Complete SQL script to create the clone table with sample data
- **`create_clone_table.py`** - Python script to execute the SQL against your PostgreSQL database
- **`README_CLONE_WORKFLOWS.md`** - This documentation file

## 🎯 Purpose

The original workflows table can be very large in production environments. This clone provides:

- **10 diverse sample workflows** covering different use cases
- **Complete schema** including marketplace authentication fields
- **Realistic test data** with various credential requirements
- **Performance indexes** matching the original table
- **Verification queries** to validate the data

## 📊 Sample Data Overview

The clone includes 10 workflows with diverse characteristics:

| ID | Name | Provider | Auth Required | Visibility | Status |
|----|------|----------|---------------|------------|--------|
| wf-001 | OpenAI Content Generator | OpenAI | ✅ | PUBLIC | ACTIVE |
| wf-002 | Multi-Provider Integration | OpenAI, GitHub, Google | ✅ | PUBLIC | ACTIVE |
| wf-003 | Data Processing Pipeline | None | ❌ | PUBLIC | ACTIVE |
| wf-004 | Slack Notification System | Slack | ✅ | PRIVATE | ACTIVE |
| wf-005 | GitHub Repository Analyzer | GitHub | ✅ | PUBLIC | ACTIVE |
| wf-006 | Email Marketing Automation | Email/SMTP | ✅ | PRIVATE | ACTIVE |
| wf-007 | Customer Support Chatbot | OpenAI | ✅ | PUBLIC | ACTIVE |
| wf-008 | Inactive Test Workflow | None | ❌ | PRIVATE | INACTIVE |
| wf-009 | Advanced AI Content Pipeline | OpenAI (3x) | ✅ | PUBLIC | ACTIVE |
| wf-010 | Discord Community Bot | Discord | ✅ | PUBLIC | ACTIVE |

## 🚀 Quick Start

### Option 1: Using Python Script (Recommended)

1. **Set up environment variables:**
   ```bash
   export DB_HOST=localhost
   export DB_PORT=5432
   export DB_USER=postgres
   export DB_PASSWORD=your_password
   export DB_NAME=your_database_name
   ```

2. **Run the Python script:**
   ```bash
   cd /Users/<USER>/Desktop/ruh_ai/backend
   python create_clone_table.py
   ```

### Option 2: Direct SQL Execution

1. **Connect to your PostgreSQL database:**
   ```bash
   psql -h localhost -U postgres -d your_database_name
   ```

2. **Execute the SQL file:**
   ```sql
   \i clone_workflows_table.sql
   ```

## 🔍 Verification

After running the script, you can verify the data:

```sql
-- Check total rows
SELECT COUNT(*) FROM workflows_clone;

-- View all workflows
SELECT id, name, env_credential_status, visibility FROM workflows_clone;

-- Check credential requirements
SELECT 
    name,
    JSON_EXTRACT(credential_summary, '$.total_requirements') as requirements,
    JSON_EXTRACT(credential_summary, '$.estimated_setup_time') as setup_time
FROM workflows_clone 
WHERE credential_summary IS NOT NULL;
```

## 🏗️ Table Structure

The clone table includes all fields from the original workflows table:

### Core Fields
- `id` - Primary key
- `name` - Workflow name
- `description` - Workflow description
- `workflow_url` - JSON workflow definition URL
- `builder_url` - Workflow builder configuration URL
- `start_nodes` - Entry points for workflow execution
- `available_nodes` - All nodes in the workflow

### Ownership & Access
- `owner_id` - Workflow owner
- `owner_type` - USER or ORGANIZATION
- `user_ids` - Array of users with access
- `visibility` - PUBLIC or PRIVATE
- `status` - ACTIVE or INACTIVE

### Marketplace Authentication Fields
- `credential_summary` - JSON with precomputed credential requirements
- `env_credential_status` - Quick status: 'not_required' or 'pending_input'

### Metadata
- `category` - Workflow category
- `tags` - JSON array of tags
- `created_at` / `updated_at` - Timestamps
- `is_imported` - Whether imported from template
- `is_customizable` - Whether users can customize

## 🧪 Testing Use Cases

This clone is perfect for testing:

1. **Marketplace Authentication System**
   - Workflows with different credential requirements
   - Various provider integrations (OpenAI, GitHub, Google, etc.)
   - Different authentication complexities

2. **Workflow Filtering & Search**
   - Public vs private workflows
   - Active vs inactive status
   - Category-based filtering
   - Tag-based search

3. **Performance Testing**
   - Small dataset for quick iterations
   - Realistic JSON structures
   - Proper indexes for query optimization

## 🔧 Customization

To modify the sample data:

1. Edit `clone_workflows_table.sql`
2. Modify the INSERT statements
3. Add/remove workflows as needed
4. Update credential_summary JSON for different auth scenarios

## 🗑️ Cleanup

To remove the clone table:

```sql
DROP TABLE IF EXISTS workflows_clone CASCADE;
```

## 📝 Notes

- The clone table is completely independent of the original workflows table
- All sample data uses realistic but fictional URLs and IDs
- Credential requirements are designed to test the marketplace authentication system
- The table includes proper indexes for performance testing

This clone provides a perfect testing environment for the marketplace authentication features while keeping your development database lightweight and manageable.
