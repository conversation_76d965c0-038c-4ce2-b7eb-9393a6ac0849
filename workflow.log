
The default interactive shell is now zsh.
To update your account to use zsh, please run `chsh -s /bin/zsh`.
For more details, please visit https://support.apple.com/kb/HT208050.
Pratham-ka-MacBook-Air:backend prathamagarwal$ cd workflow-service/
Pratham-ka-<PERSON>Book-Air:workflow-service prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting Workflow Service...
DEBUG: StartNode class loaded
DEBUG: BaseAgentComponent class loaded with is_abstract = True
DEBUG: AI component AgenticAI loaded
DEBUG: BasicLLMChain class loaded
DEBUG: ApiRequestNode class loaded
2025-08-01 16:53:16 - workflow-service - INFO - Loading all components...
2025-08-01 16:53:16 - component-loader - INFO - Loading all components...
2025-08-01 16:53:16 - component-loader - INFO - Base component path: /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/components
2025-08-01 16:53:16 - component-loader - INFO - Imported main component packages
Warning: MCP config file not found at /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/mcp_server_configs.json
2025-08-01 16:53:16 - component-loader - INFO - Found class MCPToolsComponent in module app.components.tools.mcp_tools
2025-08-01 16:53:16 - component-loader - INFO - Found class BaseNode in module app.components.core.base_node
2025-08-01 16:53:16 - component-loader - INFO - Found class SaveToFileComponent in module app.components.processing.save_to_file
2025-08-01 16:53:16 - component-loader - INFO - Found class RegexExtractorComponent in module app.components.processing.regex_extractor
2025-08-01 16:53:16 - component-loader - INFO - Found class UniversalConverterComponent in module app.components.processing.universal_converter
2025-08-01 16:53:16 - component-loader - INFO - Found class DataComposeComponent in module app.components.processing.data_compose
2025-08-01 16:53:16 - component-loader - INFO - Found class CombineTextComponent in module app.components.processing.combine_text
2025-08-01 16:53:16 - component-loader - INFO - Found class AlterMetadataComponent in module app.components.processing.alter_metadata
2025-08-01 16:53:16 - component-loader - INFO - Found class DelayComponent in module app.components.processing.delay_time
2025-08-01 16:53:16 - component-loader - INFO - Found class SplitTextComponent in module app.components.processing.split_text
2025-08-01 16:53:16 - component-loader - INFO - Found class MergeDataComponent in module app.components.processing.merge_data
2025-08-01 16:53:16 - component-loader - INFO - Found class SelectDataComponent in module app.components.processing.select_data
2025-08-01 16:53:16 - component-loader - INFO - Found class DataToDataFrameComponent in module app.components.processing.data_to_dataframe
2025-08-01 16:53:16 - component-loader - INFO - Found class MessageToDataComponent in module app.components.processing.message_to_data
2025-08-01 16:53:16 - component-loader - INFO - Found class StartNode in module app.components.io.start_node
2025-08-01 16:53:16 - component-loader - INFO - Found class SlackComponent in module app.components.hitl.slack_component
2025-08-01 16:53:16 - component-loader - INFO - Found class GmailComponent in module app.components.hitl.gmail_component
2025-08-01 16:53:16 - component-loader - INFO - Found class TelegramComponent in module app.components.hitl.telegram_component
2025-08-01 16:53:16 - component-loader - INFO - Found class EmailComponent in module app.components.hitl.email_component
2025-08-01 16:53:16 - component-loader - INFO - Found class BaseHITLComponent in module app.components.hitl.base_hitl_component
2025-08-01 16:53:16 - component-loader - INFO - Found class DiscordComponent in module app.components.hitl.discord_component
2025-08-01 16:53:16 - component-loader - INFO - Found class HITLOrchestrator in module app.components.hitl.hitl_orchestrator
2025-08-01 16:53:16 - component-loader - INFO - Found class WhatsAppComponent in module app.components.hitl.whatsapp_component
2025-08-01 16:53:16 - component-loader - INFO - Found class BaseDataInteractionComponent in module app.components.data_interaction.base_data_interaction_component
2025-08-01 16:53:16 - component-loader - INFO - Found class WebhookComponent in module app.components.data_interaction.webhook
2025-08-01 16:53:16 - component-loader - INFO - Found class ApiRequestNode in module app.components.data_interaction.api_request
2025-08-01 16:53:16 - component-loader - INFO - Found class BasicLLMChain in module app.components.ai.basic_llm_chain
2025-08-01 16:53:16 - component-loader - INFO - Found class Classifier in module app.components.ai.classifier
2025-08-01 16:53:16 - component-loader - INFO - Found class AgenticAI in module app.components.ai.agentic_ai
2025-08-01 16:53:16 - component-loader - INFO - Found class BaseAgentComponent in module app.components.ai.base_agent_component
2025-08-01 16:53:16 - component-loader - INFO - Found class QuestionAnswerModule in module app.components.ai.question_answer_module
2025-08-01 16:53:16 - component-loader - INFO - Found class StartOutboundCallComponent in module app.components.ai.outbound_caller
2025-08-01 16:53:16 - component-loader - INFO - Found class Summarizer in module app.components.ai.summarizer
2025-08-01 16:53:16 - component-loader - INFO - Found class InformationExtractor in module app.components.ai.information_extractor
2025-08-01 16:53:16 - component-loader - INFO - Found class SentimentAnalyzer in module app.components.ai.sentiment_analyzer
2025-08-01 16:53:16 - component-loader - INFO - Found class ErrorHandling in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class ExitCondition in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class IterationSettings in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class IterationSource in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class LoopConfig in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class LoopNode in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class RangeConfig in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class ResultAggregation in module app.components.control_flow.loopNode
2025-08-01 16:53:16 - component-loader - INFO - Found class ConditionalNode in module app.components.control_flow.conditionalNode
2025-08-01 16:53:16 - component-loader - INFO - Found class IDGeneratorComponent in module app.components.helper.id_generator
2025-08-01 16:53:16 - component-loader - INFO - Found class DocExtractorComponent in module app.components.helper.doc_extractor
2025-08-01 16:53:16 - component-loader - INFO - Loaded 40 component modules
2025-08-01 16:53:16 - workflow-service - INFO - Successfully loaded 40 component modules
2025-08-01 16:53:20 - workflow-service - INFO - WorkflowService initialized
Workflow service started on port 50056
2025-08-01 17:06:33 - workflow-service - INFO - updateWorkflow request received
2025-08-01 17:06:33 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:19 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:19 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:19 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:21 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:21 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:21 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:23 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:23 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:23 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:24 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:24 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:24 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:29 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:29 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:29 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:30 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:30 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:30 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:46 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:46 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:46 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:12:47 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:12:47 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb
2025-08-01 17:12:47 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:44 - workflow-service - INFO - listWorkflows request received
2025-08-01 17:15:44 [info     ] list_workflows_request         page=1 page_size=9 user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:44 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
[DEBUG] Workflow list: [id: "c57ff5ae-ff7b-4f6b-8816-cace56832118"
name: "Untitled Workflow"
description: "Untitled_Workflow"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/cce58947-df59-4a36-8d9c-0d6d3e21eb29.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/999b5488-e752-403d-9928-5270116a87f4.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:29:45.509011"
updated_at: "2025-08-01T09:35:05.660684"
is_updated: true
is_customizable: true
, id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true
]
2025-08-01 17:15:45 [info     ] workflows_retrieved            page=1 total=2 total_pages=1
2025-08-01 17:15:45 - workflow-service - INFO - listWorkflows request received
2025-08-01 17:15:45 [info     ] list_workflows_request         page=1 page_size=9 user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:45 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
[DEBUG] Workflow list: [id: "c57ff5ae-ff7b-4f6b-8816-cace56832118"
name: "Untitled Workflow"
description: "Untitled_Workflow"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/cce58947-df59-4a36-8d9c-0d6d3e21eb29.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/999b5488-e752-403d-9928-5270116a87f4.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:29:45.509011"
updated_at: "2025-08-01T09:35:05.660684"
is_updated: true
is_customizable: true
, id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true
]
2025-08-01 17:15:47 [info     ] workflows_retrieved            page=1 total=2 total_pages=1
2025-08-01 17:15:48 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:15:48 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
2025-08-01 17:15:48 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:49 [info     ] workflow_retrieved             workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[WORKFLOW TO PROTOBUF] id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true

2025-08-01 17:15:50 - workflow-service - INFO - listWorkflows request received
2025-08-01 17:15:50 [info     ] list_workflows_request         page=1 page_size=50 user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:50 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
[DEBUG] Workflow list: [id: "c57ff5ae-ff7b-4f6b-8816-cace56832118"
name: "Untitled Workflow"
description: "Untitled_Workflow"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/cce58947-df59-4a36-8d9c-0d6d3e21eb29.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/999b5488-e752-403d-9928-5270116a87f4.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:29:45.509011"
updated_at: "2025-08-01T09:35:05.660684"
is_updated: true
is_customizable: true
, id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true
]
2025-08-01 17:15:51 [info     ] workflows_retrieved            page=1 total=2 total_pages=1
2025-08-01 17:15:51 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:15:51 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
2025-08-01 17:15:51 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:52 [info     ] workflow_retrieved             workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[WORKFLOW TO PROTOBUF] id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true

2025-08-01 17:15:53 - workflow-service - INFO - listWorkflows request received
2025-08-01 17:15:53 [info     ] list_workflows_request         page=1 page_size=50 user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:53 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
[DEBUG] Workflow list: [id: "c57ff5ae-ff7b-4f6b-8816-cace56832118"
name: "Untitled Workflow"
description: "Untitled_Workflow"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/cce58947-df59-4a36-8d9c-0d6d3e21eb29.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/999b5488-e752-403d-9928-5270116a87f4.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:29:45.509011"
updated_at: "2025-08-01T09:35:05.660684"
is_updated: true
is_customizable: true
, id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true
]
2025-08-01 17:15:54 [info     ] workflows_retrieved            page=1 total=2 total_pages=1
2025-08-01 17:15:56 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:15:56 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
2025-08-01 17:15:56 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:15:56 [info     ] workflow_retrieved             workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[WORKFLOW TO PROTOBUF] id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/86ac42bf-1986-4847-9905-1b473e556952.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/b84c9f48-53ee-4175-8f49-d78fa267a29f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T09:22:53.034896"
is_customizable: true

2025-08-01 17:15:57 - workflow-service - INFO - updateWorkflow request received
2025-08-01 17:15:57 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': 60}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {}}}, 'width': 388, 'height': 238, 'selected': False, 'dragging': False}], 'edges': [], 'unconnected_nodes': [{'id': 'MCP_Google_Document_append_document-1754038223238', 'type': 'WorkflowNode', 'position': {'x': 400, 'y': 120}, 'data': {'label': 'Google Document - append_document', 'type': 'mcp', 'originalType': 'MCP_Google_Document_append_document', 'definition': {'name': 'MCP_Google_Document_append_document', 'display_name': 'Google Document - append_document', 'description': 'Append content to the end of a Google Document', 'category': 'file_handling', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'document_id', 'display_name': 'Document Id', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'content', 'display_name': 'Content', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'format', 'display_name': 'Format', 'info': '', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'plain', 'options': ['plain', 'html', 'markdown'], 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'Upload Document', 'display_name': 'Upload Document', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_google_document_append_document', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png', 'oauth_details': {'provider': 'google', 'tool_name': 'google_document', 'is_connected': True}, 'mcp_info': {'server_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_path': '', 'tool_name': 'append_document', 'input_schema': {'properties': {'document_id': {'title': 'Document Id', 'type': 'string'}, 'content': {'title': 'Content', 'type': 'string'}, 'format': {'anyOf': [{'enum': ['plain', 'html', 'markdown'], 'type': 'string'}, {'type': 'null'}], 'default': 'plain', 'title': 'Format'}}, 'required': ['document_id', 'content'], 'title': 'AppendDocument', 'type': 'object'}, 'output_schema': {'properties': {'Upload Document': {'type': 'string', 'description': 'user uploads their document', 'title': 'Upload Document'}}}}}, 'config': {'format': 'plain'}, 'oauthConnectionState': {'isConnected': True, 'provider': 'google', 'connectedAt': '2025-08-01T08:59:01.665Z'}}, 'width': 388, 'height': 384, 'selected': False, 'dragging': False, 'style': {'opacity': 0.5}}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/6f6c7c73-40ad-46c1-904c-b72aafbd449f.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/6f6c7c73-40ad-46c1-904c-b72aafbd449f.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges', 'unconnected_nodes']
[DEBUG] Number of nodes: 1
[DEBUG] Number of edges: 0
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
✅ Found 0 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 0
   - All nodes: 0

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 0 nodes
   - Grouped into 0 levels

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 0
   - Available nodes extracted: 0
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 0 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 1
   - Edges: 0
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
✅ Found 0 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 0
   - Edge mappings: 0
   - All nodes: 0

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 0 nodes
   - Grouped into 0 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/1: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (0 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 0 []
   - Final transition_nodes count: 0
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 0
   - Total transitions created: 0
   - Conditional transitions: 0
   - Regular transitions: 0

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [], 'transitions': []} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/7f45fe4b-c897-4e57-a81e-547314b99075.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/7f45fe4b-c897-4e57-a81e-547314b99075.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
✅ Found 0 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 0
   - All nodes: 0

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 0 nodes
   - Grouped into 0 levels

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 0
   - Available nodes extracted: 0
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-08-01 17:15:59 [info     ] Set is_updated=True for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3 due to version-relevant changes
2025-08-01 17:16:00 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
2025-08-01 17:16:18 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:16:18 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
2025-08-01 17:16:18 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:16:18 [info     ] workflow_retrieved             workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[WORKFLOW TO PROTOBUF] id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
description: "Untitled_Workflow_7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/7f45fe4b-c897-4e57-a81e-547314b99075.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/6f6c7c73-40ad-46c1-904c-b72aafbd449f.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T11:45:59.848421"
is_updated: true
is_customizable: true

2025-08-01 17:16:20 - workflow-service - INFO - updateWorkflow request received
2025-08-01 17:16:20 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': 60}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'MCP_Google_Document_append_document-1754038223238_document_id': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'document_id', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Google_Document_append_document-1754038223238_content': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'content', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}}}}, 'width': 388, 'height': 238, 'selected': False, 'dragging': False, 'positionAbsolute': {'x': -120, 'y': 60}}, {'id': 'MCP_Google_Document_append_document-1754038223238', 'type': 'WorkflowNode', 'position': {'x': 400, 'y': 120}, 'data': {'label': 'Google Document - append_document', 'type': 'mcp', 'originalType': 'MCP_Google_Document_append_document', 'definition': {'name': 'MCP_Google_Document_append_document', 'display_name': 'Google Document - append_document', 'description': 'Append content to the end of a Google Document', 'category': 'file_handling', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'document_id', 'display_name': 'Document Id', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'content', 'display_name': 'Content', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'format', 'display_name': 'Format', 'info': '', 'input_type': 'dropdown', 'input_types': ['dropdown', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'plain', 'options': ['plain', 'html', 'markdown'], 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'Upload Document', 'display_name': 'Upload Document', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_google_document_append_document', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png', 'oauth_details': {'provider': 'google', 'tool_name': 'google_document', 'is_connected': True}, 'mcp_info': {'server_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_path': '', 'tool_name': 'append_document', 'input_schema': {'properties': {'document_id': {'title': 'Document Id', 'type': 'string'}, 'content': {'title': 'Content', 'type': 'string'}, 'format': {'anyOf': [{'enum': ['plain', 'html', 'markdown'], 'type': 'string'}, {'type': 'null'}], 'default': 'plain', 'title': 'Format'}}, 'required': ['document_id', 'content'], 'title': 'AppendDocument', 'type': 'object'}, 'output_schema': {'properties': {'Upload Document': {'type': 'string', 'description': 'user uploads their document', 'title': 'Upload Document'}}}}}, 'config': {'format': 'plain'}, 'oauthConnectionState': {'isConnected': True, 'provider': 'google', 'connectedAt': '2025-08-01T11:46:03.126Z'}}, 'width': 388, 'height': 384, 'selected': True, 'dragging': False, 'style': {'opacity': 1}, 'positionAbsolute': {'x': 400, 'y': 120}}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MCP_Google_Document_append_document-1754038223238', 'targetHandle': 'document_id', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/51f31a2c-baff-4069-816d-9e1fee9b3eac.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/51f31a2c-baff-4069-816d-9e1fee9b3eac.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 2
[DEBUG] Number of edges: 1
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=MCP_Google_Document_append_document-1754038223238, type=mcp, originalType=MCP_Google_Document_append_document
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id, source=start-node, target=MCP_Google_Document_append_document-1754038223238, sourceHandle=flow
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 1
   - Available nodes extracted: 1
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 0 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 2
   - Edges: 1
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - MCP_Google_Document_append_document: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - Edge mappings: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/2: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/2: MCP_Google_Document_append_document-1754038223238
   Type: MCP_Google_Document_append_document (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
   🔧 Fixed MCP tool_name: append_document
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (1 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 1 ['MCP_Google_Document_append_document-1754038223238']
   - Final transition_nodes count: 1
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 1
   - Total transitions created: 1
   - Conditional transitions: 0
   - Regular transitions: 0

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'append_document', 'input_schema': {'predefined_fields': [{'field_name': 'document_id', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'content', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'format', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Upload Document', 'data_type': {'type': 'string', 'description': 'user uploads their document', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Google_Document_append_document-1754038223238', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_label': 'Google Document - append_document', 'node_info': {'node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'append_document', 'tool_params': {'items': [{'field_name': 'document_id', 'data_type': 'string', 'field_value': None}, {'field_name': 'content', 'data_type': 'string', 'field_value': None}, {'field_name': 'format', 'data_type': 'string', 'field_value': 'plain'}]}}], 'input_data': [], 'output_data': []}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'document_id', 'handle_name': 'Document Id', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'content', 'handle_name': 'Content', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'format', 'handle_name': 'Format', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Upload Document', 'handle_name': 'Upload Document', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Upload Document': 'Upload Document'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Upload Document', 'output_data.Upload Document', 'response.Upload Document', 'data.Upload Document', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Upload Document'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/42f37bd3-ae51-4b1c-95ff-d25b2e982893.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/42f37bd3-ae51-4b1c-95ff-d25b2e982893.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 1
   - Available nodes extracted: 1
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-08-01 17:16:22 [info     ] Set is_updated=True for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3 due to version-relevant changes
2025-08-01 17:16:23 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
