
🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Google_Document_append_document-1754038223238
         - Sequence: 1
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['MCP_Gmail_send_email-1754050569575']

   📦 Processing node: MCP_Gmail_send_email-1754050569575
      Type: MCP_Gmail_send_email (mcp)
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 2
   - Total transitions created: 2
   - Conditional transitions: 0
   - Regular transitions: 1

⚙️  REGULAR TRANSITIONS CREATED:
   - MCP_Google_Document_append_document-1754038223238: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'append_document', 'input_schema': {'predefined_fields': [{'field_name': 'document_id', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'content', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'format', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Upload Document', 'data_type': {'type': 'string', 'description': 'user uploads their document', 'format': 'string'}}]}}]}, {'id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'send_email', 'input_schema': {'predefined_fields': [{'field_name': 'to', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'subject', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'body', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'cc', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'bcc', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'html', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Design', 'data_type': {'type': 'string', 'description': 'Design Mail\n', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Google_Document_append_document-1754038223238', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_label': 'Google Document - append_document', 'node_info': {'node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'append_document', 'tool_params': {'items': [{'field_name': 'document_id', 'data_type': 'string', 'field_value': None}, {'field_name': 'content', 'data_type': 'string', 'field_value': None}, {'field_name': 'format', 'data_type': 'string', 'field_value': 'plain'}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-MCP_Gmail_send_email-1754050569575', 'target_node_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'Upload Document', 'result_path': 'Upload Document', 'edge_id': 'reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'document_id', 'handle_name': 'Document Id', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'content', 'handle_name': 'Content', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'format', 'handle_name': 'Format', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Upload Document', 'handle_name': 'Upload Document', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Upload Document': 'Upload Document'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Upload Document', 'output_data.Upload Document', 'response.Upload Document', 'data.Upload Document', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Upload Document'}}, 'approval_required': False, 'end': False}, {'id': 'transition-MCP_Gmail_send_email-1754050569575', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'MCP', 'node_label': 'Gmail - send_email', 'node_info': {'node_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'send_email', 'tool_params': {'items': [{'field_name': 'to', 'data_type': 'string', 'field_value': None}, {'field_name': 'subject', 'data_type': 'string', 'field_value': None}, {'field_name': 'body', 'data_type': 'string', 'field_value': None}, {'field_name': 'cc', 'data_type': 'string', 'field_value': None}, {'field_name': 'bcc', 'data_type': 'string', 'field_value': None}, {'field_name': 'html', 'data_type': 'boolean', 'field_value': None}]}}], 'input_data': [{'from_transition_id': 'transition-MCP_Google_Document_append_document-1754038223238', 'source_node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Google_Document_append_document-1754038223238', 'source_handle_id': 'Upload Document', 'target_handle_id': 'to', 'edge_id': 'reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'to', 'handle_name': 'To', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'subject', 'handle_name': 'Subject', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'body', 'handle_name': 'Body', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'cc', 'handle_name': 'Cc', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'bcc', 'handle_name': 'Bcc', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'html', 'handle_name': 'Html', 'data_type': 'boolean', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Design', 'handle_name': 'Design', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Design': 'Design'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Design', 'output_data.Design', 'response.Design', 'data.Design', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Design'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/89e670e0-4444-488e-97be-067af5dff1c7.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/89e670e0-4444-488e-97be-067af5dff1c7.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 2
   - All nodes: 3
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 2 nodes
   - Grouped into 2 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']
   - Level 1: ['MCP_Gmail_send_email-1754050569575']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['MCP_Gmail_send_email-1754050569575']

   📦 Processing level-1 node: MCP_Gmail_send_email-1754050569575
      Type: MCP_Gmail_send_email (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Gmail_send_email-1754050569575)
      🔗 MCP: MCP_Gmail_send_email (server: 37db65ab-0586-434e-a58d-7ddc6d9a8beb)
      ✅ ADDED: To available_nodes array (position #2)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 2
   - Available nodes extracted: 2
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
     2. MCP_Gmail_send_email (mcp) -> transition-MCP_Gmail_send_email-1754050569575
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-08-01 17:46:23 [info     ] Set is_updated=True for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3 due to version-relevant changes
[DEBUG] Performing credential analysis for updated workflow
[DEBUG] Credential analysis complete: status=pending_input
2025-08-01 17:46:23 [info     ] Updated credential analysis for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3: status=pending_input, requirements=1
2025-08-01 17:46:24 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
2025-08-01 17:49:37 - workflow-service - INFO - getWorkflow request received
2025-08-01 17:49:37 [info     ] get_workflow_request           user_id=c1454e90-09ac-40f2-bde2-833387d7b645 workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
2025-08-01 17:49:37 [info     ] filtering_by_user_id           user_id=c1454e90-09ac-40f2-bde2-833387d7b645
2025-08-01 17:49:38 [info     ] workflow_retrieved             workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[WORKFLOW TO PROTOBUF] id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
name: "Untitled Workflow 7"
description: "Untitled_Workflow_7"
workflow_url: "https://storage.googleapis.com/ruh-dev/workflows/89e670e0-4444-488e-97be-067af5dff1c7.json"
builder_url: "https://storage.googleapis.com/ruh-dev/workflow_builders/f48b5308-b33a-4690-bbf7-546008c0987d.json"
owner_id: "c1454e90-09ac-40f2-bde2-833387d7b645"
user_ids: "c1454e90-09ac-40f2-bde2-833387d7b645"
owner_type: "user"
version: "1.0.0"
visibility: "private"
status: "active"
created_at: "2025-08-01T09:22:52.271986"
updated_at: "2025-08-01T12:16:23.775583"
start_nodes: "{\"type\": \"string\", \"field\": \"document_id\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"
start_nodes: "{\"type\": \"string\", \"field\": \"content\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"
start_nodes: "{\"type\": \"string\", \"field\": \"subject\", \"transition_id\": \"transition-MCP_Gmail_send_email-1754050569575\"}"
start_nodes: "{\"type\": \"string\", \"field\": \"body\", \"transition_id\": \"transition-MCP_Gmail_send_email-1754050569575\"}"
available_nodes: "{\"id\": \"0931e5d9-fccc-459b-81a5-c1a251d16c7a\", \"data\": {\"input_schema\": {\"type\": \"object\", \"title\": \"AppendDocument\", \"required\": [\"document_id\", \"content\"], \"properties\": {\"format\": {\"anyOf\": [{\"enum\": [\"plain\", \"html\", \"markdown\"], \"type\": \"string\"}, {\"type\": \"null\"}], \"title\": \"Format\", \"default\": \"plain\"}, \"content\": {\"type\": \"string\", \"title\": \"Content\"}, \"document_id\": {\"type\": \"string\", \"title\": \"Document Id\"}}}, \"output_schema\": {\"properties\": {\"Upload Document\": {\"type\": \"string\", \"title\": \"Upload Document\", \"description\": \"user uploads their document\"}}}}, \"name\": \"MCP_Google_Document_append_document\", \"type\": \"mcp\", \"label\": \"Google Document - append_document\", \"env_keys\": [], \"display_name\": \"Google Document - append_document\", \"oauth_details\": {\"provider\": \"google\", \"tool_name\": \"google_document\", \"is_connected\": true}, \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\", \"env_credential_status\": \"pending_input\"}"
available_nodes: "{\"id\": \"37db65ab-0586-434e-a58d-7ddc6d9a8beb\", \"data\": {\"input_schema\": {\"type\": \"object\", \"title\": \"SendEmail\", \"required\": [\"to\", \"subject\", \"body\"], \"properties\": {\"cc\": {\"anyOf\": [{\"type\": \"array\", \"items\": {\"type\": \"string\"}}, {\"type\": \"null\"}], \"title\": \"Cc\", \"default\": null}, \"to\": {\"type\": \"string\", \"title\": \"To\"}, \"bcc\": {\"anyOf\": [{\"type\": \"array\", \"items\": {\"type\": \"string\"}}, {\"type\": \"null\"}], \"title\": \"Bcc\", \"default\": null}, \"body\": {\"type\": \"string\", \"title\": \"Body\"}, \"html\": {\"anyOf\": [{\"type\": \"boolean\"}, {\"type\": \"null\"}], \"title\": \"Html\", \"default\": false}, \"subject\": {\"type\": \"string\", \"title\": \"Subject\"}}}, \"output_schema\": {\"properties\": {\"Design\": {\"type\": \"string\", \"title\": \"Design\", \"description\": \"Design Mail\\n\"}}}}, \"name\": \"MCP_Gmail_send_email\", \"type\": \"mcp\", \"label\": \"Gmail - send_email\", \"env_keys\": [], \"display_name\": \"Gmail - send_email\", \"oauth_details\": {\"provider\": \"google\", \"tool_name\": \"gmail\", \"is_connected\": true}, \"transition_id\": \"transition-MCP_Gmail_send_email-1754050569575\", \"env_credential_status\": \"pending_input\"}"
is_updated: true
is_customizable: true

2025-08-01 17:49:39 - workflow-service - INFO - updateWorkflow request received
2025-08-01 17:49:39 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': 60}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'MCP_Google_Document_append_document-1754038223238_document_id': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'document_id', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Google_Document_append_document-1754038223238_content': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'content', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Gmail_send_email-1754050569575_subject': {'node_id': 'MCP_Gmail_send_email-1754050569575', 'node_name': 'Gmail - send_email', 'input_name': 'subject', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Gmail_send_email-1754050569575_body': {'node_id': 'MCP_Gmail_send_email-1754050569575', 'node_name': 'Gmail - send_email', 'input_name': 'body', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061_query': {'node_id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061', 'node_name': 'Tavily Web Search and Extraction Server - tavily-search', 'input_name': 'query', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}}}}, 'width': 388, 'height': 238, 'selected': False, 'dragging': False, 'positionAbsolute': {'x': -120, 'y': 60}}, {'id': 'MCP_Google_Document_append_document-1754038223238', 'type': 'WorkflowNode', 'position': {'x': 400, 'y': -140}, 'data': {'label': 'Google Document - append_document', 'type': 'mcp', 'originalType': 'MCP_Google_Document_append_document', 'definition': {'name': 'MCP_Google_Document_append_document', 'display_name': 'Google Document - append_document', 'description': 'Append content to the end of a Google Document', 'category': 'file_handling', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'document_id', 'display_name': 'Document Id', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'content', 'display_name': 'Content', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'format', 'display_name': 'Format', 'info': '', 'input_type': 'dropdown', 'input_types': ['dropdown', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'plain', 'options': ['plain', 'html', 'markdown'], 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'Upload Document', 'display_name': 'Upload Document', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_google_document_append_document', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png', 'oauth_details': {'provider': 'google', 'tool_name': 'google_document', 'is_connected': True}, 'mcp_info': {'server_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_path': '', 'tool_name': 'append_document', 'input_schema': {'properties': {'document_id': {'title': 'Document Id', 'type': 'string'}, 'content': {'title': 'Content', 'type': 'string'}, 'format': {'anyOf': [{'enum': ['plain', 'html', 'markdown'], 'type': 'string'}, {'type': 'null'}], 'default': 'plain', 'title': 'Format'}}, 'required': ['document_id', 'content'], 'title': 'AppendDocument', 'type': 'object'}, 'output_schema': {'properties': {'Upload Document': {'type': 'string', 'description': 'user uploads their document', 'title': 'Upload Document'}}}}}, 'config': {'format': 'plain'}, 'oauthConnectionState': {'isConnected': True, 'provider': 'google', 'connectedAt': '2025-08-01T12:18:30.045Z'}}, 'width': 388, 'height': 384, 'selected': False, 'dragging': False, 'style': {'opacity': 1}, 'positionAbsolute': {'x': 400, 'y': -140}}, {'id': 'MCP_Gmail_send_email-1754050569575', 'type': 'WorkflowNode', 'position': {'x': 980, 'y': 120}, 'data': {'label': 'Gmail - send_email', 'type': 'mcp', 'originalType': 'MCP_Gmail_send_email', 'definition': {'name': 'MCP_Gmail_send_email', 'display_name': 'Gmail - send_email', 'description': 'Create and send a new email message', 'category': 'notifications_alerts', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'to', 'display_name': 'To', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'subject', 'display_name': 'Subject', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'body', 'display_name': 'Body', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'cc', 'display_name': 'Cc', 'info': '', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'bcc', 'display_name': 'Bcc', 'info': '', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'html', 'display_name': 'Html', 'info': '', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'Design', 'display_name': 'Design', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_gmail_send_email', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/gmail.png/**********-gmail.png', 'oauth_details': {'provider': 'google', 'tool_name': 'gmail', 'is_connected': True}, 'mcp_info': {'server_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'server_path': '', 'tool_name': 'send_email', 'input_schema': {'properties': {'to': {'title': 'To', 'type': 'string'}, 'subject': {'title': 'Subject', 'type': 'string'}, 'body': {'title': 'Body', 'type': 'string'}, 'cc': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Cc'}, 'bcc': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Bcc'}, 'html': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': False, 'title': 'Html'}}, 'required': ['to', 'subject', 'body'], 'title': 'SendEmail', 'type': 'object'}, 'output_schema': {'properties': {'Design': {'type': 'string', 'description': 'Design Mail\n', 'title': 'Design'}}}}}, 'config': {}, 'oauthConnectionState': {'isConnected': True, 'provider': 'google', 'connectedAt': '2025-08-01T12:18:30.223Z'}}, 'style': {'opacity': 1}, 'width': 388, 'height': 519, 'positionAbsolute': {'x': 980, 'y': 120}, 'selected': False, 'dragging': False}, {'id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061', 'type': 'WorkflowNode', 'position': {'x': 1540, 'y': -40}, 'data': {'label': 'Tavily Web Search and Extraction Server - tavily-search', 'type': 'mcp', 'originalType': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'definition': {'name': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'display_name': 'Tavily Web Search and Extraction Server - tavily-search', 'description': "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", 'category': 'Tools', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'query', 'display_name': 'query', 'info': 'Search query', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'search_depth', 'display_name': 'search depth', 'info': "The depth of the search. It can be 'basic' or 'advanced'", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'basic', 'options': ['basic', 'advanced'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'topic', 'display_name': 'topic', 'info': 'The category of the search. This will determine which of our agents will be used for the search', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'general', 'options': ['general', 'news'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'days', 'display_name': 'days', 'info': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 3, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'time_range', 'display_name': 'time range', 'info': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'max_results', 'display_name': 'max results', 'info': 'The maximum number of search results to return', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 10, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_images', 'display_name': 'include images', 'info': 'Include a list of query-related images in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_image_descriptions', 'display_name': 'include image descriptions', 'info': 'Include a list of query-related images and their descriptions in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_raw_content', 'display_name': 'include raw content', 'info': 'Include the cleaned and parsed HTML content of each search result', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_domains', 'display_name': 'include domains', 'info': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'exclude_domains', 'display_name': 'exclude domains', 'info': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'content', 'display_name': 'content', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_tavily_web_search_and_extraction_server_tavily-search', 'type': 'MCP', 'env_keys': [{'key': 'TAVILY_API_KEY', 'description': 'tavily api key'}], 'env_credential_status': 'provided', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/tavily.png/1750839496-tavily.png', 'oauth_details': None, 'mcp_info': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'type': 'object', 'properties': {'content': {'type': 'string', 'description': 'generated content from tavily', 'title': 'content'}}, 'required': ['content']}}}, 'config': {'search_depth': 'basic', 'topic': 'general', 'days': 3, 'max_results': 10, 'include_domains': [], 'exclude_domains': []}}, 'style': {'opacity': 1}, 'width': 388, 'height': 723, 'selected': False, 'positionAbsolute': {'x': 1540, 'y': -40}, 'dragging': False}], 'edges': [{'id': 'reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id', 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MCP_Google_Document_append_document-1754038223238', 'targetHandle': 'document_id', 'type': 'default', 'animated': True, 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'zIndex': 5}, 'source': 'MCP_Google_Document_append_document-1754038223238', 'sourceHandle': 'Upload Document', 'target': 'MCP_Gmail_send_email-1754050569575', 'targetHandle': 'to', 'type': 'default', 'id': 'reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'zIndex': 5}, 'source': 'MCP_Gmail_send_email-1754050569575', 'sourceHandle': 'Design', 'target': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061', 'targetHandle': 'time_range', 'type': 'default', 'id': 'reactflow__edge-MCP_Gmail_send_email-1754050569575Design-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061time_range'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/08ab5143-0b3e-45a4-9dc2-497002cef6f5.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/08ab5143-0b3e-45a4-9dc2-497002cef6f5.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 4
[DEBUG] Number of edges: 3
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=MCP_Google_Document_append_document-1754038223238, type=mcp, originalType=MCP_Google_Document_append_document
[DEBUG] Node 2: id=MCP_Gmail_send_email-1754050569575, type=mcp, originalType=MCP_Gmail_send_email
[DEBUG] Node 3: id=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061, type=mcp, originalType=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id, source=start-node, target=MCP_Google_Document_append_document-1754038223238, sourceHandle=flow
[DEBUG] Edge 1: id=reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to, source=MCP_Google_Document_append_document-1754038223238, target=MCP_Gmail_send_email-1754050569575, sourceHandle=Upload Document
[DEBUG] Edge 2: id=reactflow__edge-MCP_Gmail_send_email-1754050569575Design-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061time_range, source=MCP_Gmail_send_email-1754050569575, target=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061, sourceHandle=Design
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']
   - Level 1: ['MCP_Gmail_send_email-1754050569575']
   - Level 2: ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['MCP_Gmail_send_email-1754050569575']

   📦 Processing level-1 node: MCP_Gmail_send_email-1754050569575
      Type: MCP_Gmail_send_email (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Gmail_send_email-1754050569575)
      🔗 MCP: MCP_Gmail_send_email (server: 37db65ab-0586-434e-a58d-7ddc6d9a8beb)
      ✅ ADDED: To available_nodes array (position #2)

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']

   📦 Processing level-2 node: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
      Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061)
      🔗 MCP: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (server: fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4)
      ✅ ADDED: To available_nodes array (position #3)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 3
   - Available nodes extracted: 3
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
     2. MCP_Gmail_send_email (mcp) -> transition-MCP_Gmail_send_email-1754050569575
     3. MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp) -> transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 0 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 4
   - Edges: 3
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - MCP_Google_Document_append_document: 1
   - MCP_Gmail_send_email: 1
   - MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - Edge mappings: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/4: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/4: MCP_Google_Document_append_document-1754038223238
   Type: MCP_Google_Document_append_document (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
   🔧 Fixed MCP tool_name: append_document
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/4: MCP_Gmail_send_email-1754050569575
   Type: MCP_Gmail_send_email (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
   🔧 Fixed MCP tool_name: send_email
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 4/4: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
   Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
   🔧 Fixed MCP tool_name: tavily-search
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (3 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 3 ['MCP_Google_Document_append_document-1754038223238', 'MCP_Gmail_send_email-1754050569575', 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']
   - Final transition_nodes count: 3
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Google_Document_append_document-1754038223238
         - Sequence: 1
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['MCP_Gmail_send_email-1754050569575']

   📦 Processing node: MCP_Gmail_send_email-1754050569575
      Type: MCP_Gmail_send_email (mcp)
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Gmail_send_email-1754050569575): node_type='mcp', original_type='MCP_Gmail_send_email', result=False
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Gmail_send_email-1754050569575
         - Sequence: 2
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 1
         - Output Data: 1

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']

   📦 Processing node: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
      Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 3
   - Total transitions created: 3
   - Conditional transitions: 0
   - Regular transitions: 2

⚙️  REGULAR TRANSITIONS CREATED:
   - MCP_Google_Document_append_document-1754038223238: 1 tools
   - MCP_Gmail_send_email-1754050569575: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'append_document', 'input_schema': {'predefined_fields': [{'field_name': 'document_id', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'content', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'format', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Upload Document', 'data_type': {'type': 'string', 'description': 'user uploads their document', 'format': 'string'}}]}}]}, {'id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'send_email', 'input_schema': {'predefined_fields': [{'field_name': 'to', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'subject', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'body', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'cc', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'bcc', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'html', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Design', 'data_type': {'type': 'string', 'description': 'Design Mail\n', 'format': 'string'}}]}}]}, {'id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'tavily-search', 'input_schema': {'predefined_fields': [{'field_name': 'query', 'data_type': {'type': 'string', 'description': 'Search query'}, 'required': True}, {'field_name': 'search_depth', 'data_type': {'type': 'string', 'description': "The depth of the search. It can be 'basic' or 'advanced'"}, 'required': False}, {'field_name': 'topic', 'data_type': {'type': 'string', 'description': 'The category of the search. This will determine which of our agents will be used for the search'}, 'required': False}, {'field_name': 'days', 'data_type': {'type': 'string', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, 'required': False}, {'field_name': 'time_range', 'data_type': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, 'required': False}, {'field_name': 'max_results', 'data_type': {'type': 'string', 'description': 'The maximum number of search results to return'}, 'required': False}, {'field_name': 'include_images', 'data_type': {'type': 'string', 'description': 'Include a list of query-related images in the response'}, 'required': False}, {'field_name': 'include_image_descriptions', 'data_type': {'type': 'string', 'description': 'Include a list of query-related images and their descriptions in the response'}, 'required': False}, {'field_name': 'include_raw_content', 'data_type': {'type': 'string', 'description': 'Include the cleaned and parsed HTML content of each search result'}, 'required': False}, {'field_name': 'include_domains', 'data_type': {'type': 'string', 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'items': {'type': 'string'}}, 'required': False}, {'field_name': 'exclude_domains', 'data_type': {'type': 'string', 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'items': {'type': 'string'}}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'content', 'data_type': {'type': 'string', 'description': 'generated content from tavily', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Google_Document_append_document-1754038223238', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_label': 'Google Document - append_document', 'node_info': {'node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'append_document', 'tool_params': {'items': [{'field_name': 'document_id', 'data_type': 'string', 'field_value': None}, {'field_name': 'content', 'data_type': 'string', 'field_value': None}, {'field_name': 'format', 'data_type': 'string', 'field_value': 'plain'}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-MCP_Gmail_send_email-1754050569575', 'target_node_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'Upload Document', 'result_path': 'Upload Document', 'edge_id': 'reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'document_id', 'handle_name': 'Document Id', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'content', 'handle_name': 'Content', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'format', 'handle_name': 'Format', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Upload Document', 'handle_name': 'Upload Document', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Upload Document': 'Upload Document'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Upload Document', 'output_data.Upload Document', 'response.Upload Document', 'data.Upload Document', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Upload Document'}}, 'approval_required': False, 'end': False}, {'id': 'transition-MCP_Gmail_send_email-1754050569575', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'MCP', 'node_label': 'Gmail - send_email', 'node_info': {'node_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'send_email', 'tool_params': {'items': [{'field_name': 'to', 'data_type': 'string', 'field_value': None}, {'field_name': 'subject', 'data_type': 'string', 'field_value': None}, {'field_name': 'body', 'data_type': 'string', 'field_value': None}, {'field_name': 'cc', 'data_type': 'string', 'field_value': None}, {'field_name': 'bcc', 'data_type': 'string', 'field_value': None}, {'field_name': 'html', 'data_type': 'boolean', 'field_value': None}]}}], 'input_data': [{'from_transition_id': 'transition-MCP_Google_Document_append_document-1754038223238', 'source_node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Google_Document_append_document-1754038223238', 'source_handle_id': 'Upload Document', 'target_handle_id': 'to', 'edge_id': 'reactflow__edge-MCP_Google_Document_append_document-1754038223238Upload Document-MCP_Gmail_send_email-1754050569575to'}]}], 'output_data': [{'to_transition_id': 'transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061', 'target_node_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'Design', 'result_path': 'Design', 'edge_id': 'reactflow__edge-MCP_Gmail_send_email-1754050569575Design-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061time_range'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'to', 'handle_name': 'To', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'subject', 'handle_name': 'Subject', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'body', 'handle_name': 'Body', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'cc', 'handle_name': 'Cc', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'bcc', 'handle_name': 'Bcc', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'html', 'handle_name': 'Html', 'data_type': 'boolean', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Design', 'handle_name': 'Design', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Design': 'Design'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Design', 'output_data.Design', 'response.Design', 'data.Design', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Design'}}, 'approval_required': False, 'end': False}, {'id': 'transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061', 'sequence': 3, 'transition_type': 'standard', 'execution_type': 'MCP', 'node_label': 'Tavily Web Search and Extraction Server - tavily-search', 'node_info': {'node_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'tavily-search', 'tool_params': {'items': [{'field_name': 'query', 'data_type': 'string', 'field_value': None}, {'field_name': 'search_depth', 'data_type': 'string', 'field_value': 'basic'}, {'field_name': 'topic', 'data_type': 'string', 'field_value': 'general'}, {'field_name': 'days', 'data_type': 'string', 'field_value': 3}, {'field_name': 'time_range', 'data_type': 'string', 'field_value': None}, {'field_name': 'max_results', 'data_type': 'string', 'field_value': 10}, {'field_name': 'include_images', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_image_descriptions', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_raw_content', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_domains', 'data_type': 'string', 'field_value': []}, {'field_name': 'exclude_domains', 'data_type': 'string', 'field_value': []}]}}], 'input_data': [{'from_transition_id': 'transition-MCP_Gmail_send_email-1754050569575', 'source_node_id': '37db65ab-0586-434e-a58d-7ddc6d9a8beb', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Gmail_send_email-1754050569575', 'source_handle_id': 'Design', 'target_handle_id': 'time_range', 'edge_id': 'reactflow__edge-MCP_Gmail_send_email-1754050569575Design-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061time_range'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'query', 'data_type': 'string', 'required': True, 'description': 'Search query'}, {'handle_id': 'search_depth', 'handle_name': 'search depth', 'data_type': 'string', 'required': False, 'description': "The depth of the search. It can be 'basic' or 'advanced'"}, {'handle_id': 'topic', 'handle_name': 'topic', 'data_type': 'string', 'required': False, 'description': 'The category of the search. This will determine which of our agents will be used for the search'}, {'handle_id': 'days', 'handle_name': 'days', 'data_type': 'string', 'required': False, 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, {'handle_id': 'time_range', 'handle_name': 'time range', 'data_type': 'string', 'required': False, 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, {'handle_id': 'max_results', 'handle_name': 'max results', 'data_type': 'string', 'required': False, 'description': 'The maximum number of search results to return'}, {'handle_id': 'include_images', 'handle_name': 'include images', 'data_type': 'boolean', 'required': False, 'description': 'Include a list of query-related images in the response'}, {'handle_id': 'include_image_descriptions', 'handle_name': 'include image descriptions', 'data_type': 'boolean', 'required': False, 'description': 'Include a list of query-related images and their descriptions in the response'}, {'handle_id': 'include_raw_content', 'handle_name': 'include raw content', 'data_type': 'boolean', 'required': False, 'description': 'Include the cleaned and parsed HTML content of each search result'}, {'handle_id': 'include_domains', 'handle_name': 'include domains', 'data_type': 'string', 'required': False, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site'}, {'handle_id': 'exclude_domains', 'handle_name': 'exclude domains', 'data_type': 'string', 'required': False, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site'}], 'output_handles': [{'handle_id': 'content', 'handle_name': 'content', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'content': 'content'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.content', 'output_data.content', 'response.content', 'data.content', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'content'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/1fb6dd18-4548-4943-b203-d0edcfcf8745.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/1fb6dd18-4548-4943-b203-d0edcfcf8745.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 3 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']
   - Level 1: ['MCP_Gmail_send_email-1754050569575']
   - Level 2: ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['MCP_Gmail_send_email-1754050569575']

   📦 Processing level-1 node: MCP_Gmail_send_email-1754050569575
      Type: MCP_Gmail_send_email (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Gmail_send_email-1754050569575)
      🔗 MCP: MCP_Gmail_send_email (server: 37db65ab-0586-434e-a58d-7ddc6d9a8beb)
      ✅ ADDED: To available_nodes array (position #2)

🏗️  PROCESSING LEVEL 2
   Nodes at this level: ['MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061']

   📦 Processing level-2 node: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
      Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061)
      🔗 MCP: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (server: fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4)
      ✅ ADDED: To available_nodes array (position #3)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 3
   - Available nodes extracted: 3
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
     2. MCP_Gmail_send_email (mcp) -> transition-MCP_Gmail_send_email-1754050569575
     3. MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp) -> transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1754050764061
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-08-01 17:49:41 [info     ] Set is_updated=True for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3 due to version-relevant changes
[DEBUG] Performing credential analysis for updated workflow
Error in workflow authentication analysis: unhashable type: 'dict'
[DEBUG] Credential analysis complete: status=not_required
2025-08-01 17:49:41 [info     ] Updated credential analysis for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3: status=not_required, requirements=0
2025-08-01 17:49:43 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
