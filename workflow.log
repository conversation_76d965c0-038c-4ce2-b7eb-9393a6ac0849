        self._state.termination_event.is_set,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        timeout=timeout,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 156, in wait
    _wait_once(wait_fn, MAXIMUM_WAIT_TIMEOUT, spin_cb)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 116, in _wait_once
    wait_fn(timeout=timeout)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 659, in wait
    signaled = self._cond.wait(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
^CPratham-ka-MacBook-Air:workflow-service prathamagarwal$ 
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ clear
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting Workflow Service...
DEBUG: StartNode class loaded
DEBUG: BaseAgentComponent class loaded with is_abstract = True
DEBUG: AI component AgenticAI loaded
DEBUG: BasicLLMChain class loaded
DEBUG: ApiRequestNode class loaded
2025-08-01 17:32:58 - workflow-service - INFO - Loading all components...
2025-08-01 17:32:58 - component-loader - INFO - Loading all components...
2025-08-01 17:32:58 - component-loader - INFO - Base component path: /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/components
2025-08-01 17:32:58 - component-loader - INFO - Imported main component packages
Warning: MCP config file not found at /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/mcp_server_configs.json
2025-08-01 17:32:58 - component-loader - INFO - Found class MCPToolsComponent in module app.components.tools.mcp_tools
2025-08-01 17:32:58 - component-loader - INFO - Found class BaseNode in module app.components.core.base_node
2025-08-01 17:32:58 - component-loader - INFO - Found class SaveToFileComponent in module app.components.processing.save_to_file
2025-08-01 17:32:58 - component-loader - INFO - Found class RegexExtractorComponent in module app.components.processing.regex_extractor
2025-08-01 17:32:58 - component-loader - INFO - Found class UniversalConverterComponent in module app.components.processing.universal_converter
2025-08-01 17:32:58 - component-loader - INFO - Found class DataComposeComponent in module app.components.processing.data_compose
2025-08-01 17:32:58 - component-loader - INFO - Found class CombineTextComponent in module app.components.processing.combine_text
2025-08-01 17:32:58 - component-loader - INFO - Found class AlterMetadataComponent in module app.components.processing.alter_metadata
2025-08-01 17:32:58 - component-loader - INFO - Found class DelayComponent in module app.components.processing.delay_time
2025-08-01 17:32:58 - component-loader - INFO - Found class SplitTextComponent in module app.components.processing.split_text
2025-08-01 17:32:58 - component-loader - INFO - Found class MergeDataComponent in module app.components.processing.merge_data
2025-08-01 17:32:58 - component-loader - INFO - Found class SelectDataComponent in module app.components.processing.select_data
2025-08-01 17:32:58 - component-loader - INFO - Found class DataToDataFrameComponent in module app.components.processing.data_to_dataframe
2025-08-01 17:32:58 - component-loader - INFO - Found class MessageToDataComponent in module app.components.processing.message_to_data
2025-08-01 17:32:58 - component-loader - INFO - Found class StartNode in module app.components.io.start_node
2025-08-01 17:32:58 - component-loader - INFO - Found class SlackComponent in module app.components.hitl.slack_component
2025-08-01 17:32:58 - component-loader - INFO - Found class GmailComponent in module app.components.hitl.gmail_component
2025-08-01 17:32:58 - component-loader - INFO - Found class TelegramComponent in module app.components.hitl.telegram_component
2025-08-01 17:32:58 - component-loader - INFO - Found class EmailComponent in module app.components.hitl.email_component
2025-08-01 17:32:58 - component-loader - INFO - Found class BaseHITLComponent in module app.components.hitl.base_hitl_component
2025-08-01 17:32:58 - component-loader - INFO - Found class DiscordComponent in module app.components.hitl.discord_component
2025-08-01 17:32:58 - component-loader - INFO - Found class HITLOrchestrator in module app.components.hitl.hitl_orchestrator
2025-08-01 17:32:58 - component-loader - INFO - Found class WhatsAppComponent in module app.components.hitl.whatsapp_component
2025-08-01 17:32:58 - component-loader - INFO - Found class BaseDataInteractionComponent in module app.components.data_interaction.base_data_interaction_component
2025-08-01 17:32:58 - component-loader - INFO - Found class WebhookComponent in module app.components.data_interaction.webhook
2025-08-01 17:32:58 - component-loader - INFO - Found class ApiRequestNode in module app.components.data_interaction.api_request
2025-08-01 17:32:58 - component-loader - INFO - Found class BasicLLMChain in module app.components.ai.basic_llm_chain
2025-08-01 17:32:58 - component-loader - INFO - Found class Classifier in module app.components.ai.classifier
2025-08-01 17:32:58 - component-loader - INFO - Found class AgenticAI in module app.components.ai.agentic_ai
2025-08-01 17:32:58 - component-loader - INFO - Found class BaseAgentComponent in module app.components.ai.base_agent_component
2025-08-01 17:32:58 - component-loader - INFO - Found class QuestionAnswerModule in module app.components.ai.question_answer_module
2025-08-01 17:32:58 - component-loader - INFO - Found class StartOutboundCallComponent in module app.components.ai.outbound_caller
2025-08-01 17:32:58 - component-loader - INFO - Found class Summarizer in module app.components.ai.summarizer
2025-08-01 17:32:58 - component-loader - INFO - Found class InformationExtractor in module app.components.ai.information_extractor
2025-08-01 17:32:58 - component-loader - INFO - Found class SentimentAnalyzer in module app.components.ai.sentiment_analyzer
2025-08-01 17:32:58 - component-loader - INFO - Found class ErrorHandling in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class ExitCondition in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class IterationSettings in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class IterationSource in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class LoopConfig in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class LoopNode in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class RangeConfig in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class ResultAggregation in module app.components.control_flow.loopNode
2025-08-01 17:32:58 - component-loader - INFO - Found class ConditionalNode in module app.components.control_flow.conditionalNode
2025-08-01 17:32:58 - component-loader - INFO - Found class IDGeneratorComponent in module app.components.helper.id_generator
2025-08-01 17:32:58 - component-loader - INFO - Found class DocExtractorComponent in module app.components.helper.doc_extractor
2025-08-01 17:32:58 - component-loader - INFO - Loaded 40 component modules
2025-08-01 17:32:58 - workflow-service - INFO - Successfully loaded 40 component modules
2025-08-01 17:33:01 - workflow-service - INFO - WorkflowService initialized
Workflow service started on port 50056
2025-08-01 17:33:17 - workflow-service - INFO - updateWorkflow request received
2025-08-01 17:33:17 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=9a6cd844-d5d0-4211-8a7c-88bd539267c3
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': 60}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'MCP_Google_Document_append_document-1754038223238_document_id': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'document_id', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Google_Document_append_document-1754038223238_content': {'node_id': 'MCP_Google_Document_append_document-1754038223238', 'node_name': 'Google Document - append_document', 'input_name': 'content', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}}}}, 'width': 388, 'height': 238, 'selected': False, 'dragging': False, 'positionAbsolute': {'x': -120, 'y': 60}}, {'id': 'MCP_Google_Document_append_document-1754038223238', 'type': 'WorkflowNode', 'position': {'x': 400, 'y': 120}, 'data': {'label': 'Google Document - append_document', 'type': 'mcp', 'originalType': 'MCP_Google_Document_append_document', 'definition': {'name': 'MCP_Google_Document_append_document', 'display_name': 'Google Document - append_document', 'description': 'Append content to the end of a Google Document', 'category': 'file_handling', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'document_id', 'display_name': 'Document Id', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'content', 'display_name': 'Content', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'format', 'display_name': 'Format', 'info': '', 'input_type': 'dropdown', 'input_types': ['dropdown', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'plain', 'options': ['plain', 'html', 'markdown'], 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'Upload Document', 'display_name': 'Upload Document', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_google_document_append_document', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'logo': 'https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png', 'oauth_details': {'provider': 'google', 'tool_name': 'google_document', 'is_connected': True}, 'mcp_info': {'server_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_path': '', 'tool_name': 'append_document', 'input_schema': {'properties': {'document_id': {'title': 'Document Id', 'type': 'string'}, 'content': {'title': 'Content', 'type': 'string'}, 'format': {'anyOf': [{'enum': ['plain', 'html', 'markdown'], 'type': 'string'}, {'type': 'null'}], 'default': 'plain', 'title': 'Format'}}, 'required': ['document_id', 'content'], 'title': 'AppendDocument', 'type': 'object'}, 'output_schema': {'properties': {'Upload Document': {'type': 'string', 'description': 'user uploads their document', 'title': 'Upload Document'}}}}}, 'config': {'format': 'plain'}, 'oauthConnectionState': {'isConnected': True, 'provider': 'google', 'connectedAt': '2025-08-01T11:51:13.146Z'}}, 'width': 388, 'height': 384, 'selected': True, 'dragging': False, 'style': {'opacity': 1}, 'positionAbsolute': {'x': 400, 'y': 120}}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MCP_Google_Document_append_document-1754038223238', 'targetHandle': 'document_id', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/19762e7f-1f93-4afc-80d0-b0a3ddcc1d0d.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/19762e7f-1f93-4afc-80d0-b0a3ddcc1d0d.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 2
[DEBUG] Number of edges: 1
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=MCP_Google_Document_append_document-1754038223238, type=mcp, originalType=MCP_Google_Document_append_document
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id, source=start-node, target=MCP_Google_Document_append_document-1754038223238, sourceHandle=flow
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 1
   - Available nodes extracted: 1
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 0 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 2
   - Edges: 1
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - MCP_Google_Document_append_document: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - Edge mappings: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/2: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/2: MCP_Google_Document_append_document-1754038223238
   Type: MCP_Google_Document_append_document (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
   🔧 Fixed MCP tool_name: append_document
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (1 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 1 ['MCP_Google_Document_append_document-1754038223238']
   - Final transition_nodes count: 1
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', result=False
[DEBUG] is_conditional_node(MCP_Google_Document_append_document-1754038223238): node_type='mcp', original_type='MCP_Google_Document_append_document', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 1
   - Total transitions created: 1
   - Conditional transitions: 0
   - Regular transitions: 0

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'append_document', 'input_schema': {'predefined_fields': [{'field_name': 'document_id', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'content', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'format', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Upload Document', 'data_type': {'type': 'string', 'description': 'user uploads their document', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Google_Document_append_document-1754038223238', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_label': 'Google Document - append_document', 'node_info': {'node_id': '0931e5d9-fccc-459b-81a5-c1a251d16c7a', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'append_document', 'tool_params': {'items': [{'field_name': 'document_id', 'data_type': 'string', 'field_value': None}, {'field_name': 'content', 'data_type': 'string', 'field_value': None}, {'field_name': 'format', 'data_type': 'string', 'field_value': 'plain'}]}}], 'input_data': [], 'output_data': []}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'document_id', 'handle_name': 'Document Id', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'content', 'handle_name': 'Content', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'format', 'handle_name': 'Format', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'Upload Document', 'handle_name': 'Upload Document', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Upload Document': 'Upload Document'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Upload Document', 'output_data.Upload Document', 'response.Upload Document', 'data.Upload Document', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Upload Document'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/d7bb4308-fe06-42da-8353-f7d3bfe99647.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/d7bb4308-fe06-42da-8353-f7d3bfe99647.json
🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER
======================================================================

🎯 IDENTIFYING START NODE...
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MCP_Google_Document_append_document-1754038223238
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 1
   - All nodes: 2
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 1 nodes
   - Grouped into 1 levels
   - Level 0: ['MCP_Google_Document_append_document-1754038223238']

🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...
==================================================

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Google_Document_append_document-1754038223238']

   📦 Processing level-0 node: MCP_Google_Document_append_document-1754038223238
      Type: MCP_Google_Document_append_document (mcp)
      ✅ PROCESSING: Available node (transition_id: transition-MCP_Google_Document_append_document-1754038223238)
      🔗 MCP: MCP_Google_Document_append_document (server: 0931e5d9-fccc-459b-81a5-c1a251d16c7a)
      ✅ ADDED: To available_nodes array (position #1)

📊 AVAILABLE NODES EXTRACTION SUMMARY:
   - Total nodes processed by level: 1
   - Available nodes extracted: 1
   - Extraction order: LEVEL-BASED (matches transition execution order) ✅
   - Final available_nodes sequence (by execution level):
     1. MCP_Google_Document_append_document (mcp) -> transition-MCP_Google_Document_append_document-1754038223238
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-08-01 17:33:24 [info     ] Set is_updated=True for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3 due to version-relevant changes
[DEBUG] Performing credential analysis for updated workflow
[DEBUG] Credential analysis complete: status=pending_input
2025-08-01 17:33:24 [info     ] Updated credential analysis for workflow 9a6cd844-d5d0-4211-8a7c-88bd539267c3: status=pending_input, requirements=1
2025-08-01 17:33:24 [error    ] workflow_update_failed         error=(builtins.TypeError) Object of type datetime is not JSON serializable
[SQL: UPDATE workflows_clone SET workflow_url=%(workflow_url)s, builder_url=%(builder_url)s, available_nodes=%(available_nodes)s::JSON, updated_at=%(updated_at)s, credential_summary=%(credential_summary)s::JSON, env_credential_status=%(env_credential_status)s WHERE workflows_clone.id = %(workflows_clone_id)s]
[parameters: [{'workflow_url': 'https://storage.googleapis.com/ruh-dev/workflows/d7bb4308-fe06-42da-8353-f7d3bfe99647.json', 'credential_summary': {'total_requireme ... (1497 characters truncated) ... age.googleapis.com/ruh-dev/workflow_builders/19762e7f-1f93-4afc-80d0-b0a3ddcc1d0d.json', 'workflows_clone_id': '9a6cd844-d5d0-4211-8a7c-88bd539267c3'}]]
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1811, in _execute_context
    context = constructor(
        dialect, self, conn, execution_options, *args, **kw
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 1519, in _init_compiled
    flattened_processors[key](compiled_params[key])
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/sql/sqltypes.py", line 2792, in process
    return json_serializer(value)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type datetime is not JSON serializable

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/services/workflow_functions.py", line 1108, in updateWorkflow
    db.commit()
    ~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2032, in commit
    trans.commit(_to_root=True)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "<string>", line 2, in commit
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1313, in commit
    self._prepare_impl()
    ~~~~~~~~~~~~~~~~~~^^
  File "<string>", line 2, in _prepare_impl
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1288, in _prepare_impl
    self.session.flush()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4345, in flush
    self._flush(objects)
    ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4480, in _flush
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4441, in _flush
    flush_context.execute()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 466, in execute
    rec.execute(self)
    ~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self.mapper,
        ^^^^^^^^^^^^
        uow.states_for_mapper_hierarchy(self.mapper, False, False),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        uow,
        ^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 85, in save_obj
    _emit_update_statements(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        base_mapper,
        ^^^^^^^^^^^^
    ...<3 lines>...
        update,
        ^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        statement, multiparams, execution_options=execution_options
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1817, in _execute_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str(statement), parameters, None, None
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1811, in _execute_context
    context = constructor(
        dialect, self, conn, execution_options, *args, **kw
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 1519, in _init_compiled
    flattened_processors[key](compiled_params[key])
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/sqlalchemy/sql/sqltypes.py", line 2792, in process
    return json_serializer(value)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
sqlalchemy.exc.StatementError: (builtins.TypeError) Object of type datetime is not JSON serializable
[SQL: UPDATE workflows_clone SET workflow_url=%(workflow_url)s, builder_url=%(builder_url)s, available_nodes=%(available_nodes)s::JSON, updated_at=%(updated_at)s, credential_summary=%(credential_summary)s::JSON, env_credential_status=%(env_credential_status)s WHERE workflows_clone.id = %(workflows_clone_id)s]
[parameters: [{'workflow_url': 'https://storage.googleapis.com/ruh-dev/workflows/d7bb4308-fe06-42da-8353-f7d3bfe99647.json', 'credential_summary': {'total_requireme ... (1497 characters truncated) ... age.googleapis.com/ruh-dev/workflow_builders/19762e7f-1f93-4afc-80d0-b0a3ddcc1d0d.json', 'workflows_clone_id': '9a6cd844-d5d0-4211-8a7c-88bd539267c3'}]]
