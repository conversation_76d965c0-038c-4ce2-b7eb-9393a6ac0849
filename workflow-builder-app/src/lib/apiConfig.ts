/**
 * API Configuration
 *
 * This file centralizes all API URL configurations to ensure consistency
 * across the application.
 *
 * Note: The API_BASE_URL already includes '/api/v1' from the environment variable
 * (e.g., https://app-dev.rapidinnovation.dev/api/v1)
 */

// Base API URL from environment variable (already includes /api/v1)
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL!;

// Auth-specific endpoints (don't add /api/v1 again)
export const AUTH_API_URL = `${API_BASE_URL}/auth`;

// Workflow-specific endpoints (don't add /api/v1 again)
export const WORKFLOWS_API_URL = `${API_BASE_URL}/workflows`;

// MCP-specific endpoints (don't add /api/v1 again)
export const MCPS_API_URL = `${API_BASE_URL}/mcps`;

// Credential-specific endpoints (don't add /api/v1 again)
export const CREDENTIALS_API_URL = `${API_BASE_URL}/credentials`;

// Model Provider-specific endpoints (don't add /api/v1 again)
export const PROVIDERS_API_URL = `${API_BASE_URL}/providers`;
export const MODELS_API_URL = `${API_BASE_URL}/models`;

// Workflow execution API URL (separate service)
export const WORKFLOW_EXECUTION_URL = process.env.NEXT_PUBLIC_WORKFLOW_EXECUTION_URL!;
export const WORKFLOW_TRIGGER_API_URL = process.env.NEXT_PUBLIC_TRIGGER_URL;

// Marketplace URL for MCP components
export const MARKETPLACE_URL = process.env.NEXT_PUBLIC_MARKETPLACE_URL;

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: `${AUTH_API_URL}/login`,
    REGISTER: `${AUTH_API_URL}/register`,
    LOGOUT: `${AUTH_API_URL}/logout`,
    REFRESH: `${AUTH_API_URL}/refresh`,
    FORGOT_PASSWORD: `${AUTH_API_URL}/forgot-password`,
    RESET_PASSWORD: `${AUTH_API_URL}/reset-password`,
    VERIFY_EMAIL: `${AUTH_API_URL}/verify-email`,
    VERIFY_EMAIL_OTP: `${AUTH_API_URL}/verify-email-otp`,
    UPDATE_PASSWORD: `${AUTH_API_URL}/update-password`,
  },

  // Workflow endpoints
  WORKFLOWS: {
    LIST: `http://localhost:8000/api/v1/workflows`,
    CREATE: `${WORKFLOWS_API_URL}`,
    GET: (id: string) => `http://localhost:8000/api/v1/workflows/${id}`,
    UPDATE: (id: string) => `http://localhost:8000/api/v1/workflows/${id}`,
    DELETE: (id: string) => `${WORKFLOWS_API_URL}/${id}`,
    EXECUTE: (id: string) => `${WORKFLOWS_API_URL}/${id}/execute`,
  },

  // MCP endpoints
  MCPS: {
    LIST: `${MCPS_API_URL}`,
    GET: (id: string) => `${MCPS_API_URL}/${id}`,
    UPDATE_OUTPUT_SCHEMA: `${API_BASE_URL}/mcps/tool/output-schema`,
    UPDATE_ENV_KEYS: `${API_BASE_URL}/mcps/env-update`,
  },

  // Credential endpoints
  CREDENTIALS: {
    LIST: `${CREDENTIALS_API_URL}`,
    CREATE: `${CREDENTIALS_API_URL}`,
    GET: (id: string) => `${CREDENTIALS_API_URL}/${id}`,
    UPDATE: (id: string) => `${CREDENTIALS_API_URL}/${id}`,
    DELETE: (id: string) => `${CREDENTIALS_API_URL}/${id}`,
  },

  // Provider endpoints
  PROVIDERS: {
    LIST: `${PROVIDERS_API_URL}`,
    GET: (id: string) => `${PROVIDERS_API_URL}/${id}`,
    MODELS: (providerId: string) => `${PROVIDERS_API_URL}/${providerId}/models`,
  },

  // Model endpoints
  MODELS: {
    LIST: `${MODELS_API_URL}`,
    GET: (id: string) => `${MODELS_API_URL}/${id}`,
    BY_PROVIDER: (providerId: string) => `${PROVIDERS_API_URL}/${providerId}/models`,
  },

  // Workflow Execution endpoints (separate service)
  WORKFLOW_EXECUTION: {
    EXECUTE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/execute`,
    APPROVE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/approve`,
    STREAM: `${WORKFLOW_EXECUTION_URL}/workflow-execute/stream`,
    RE_EXECUTE: `${WORKFLOW_EXECUTION_URL}/workflow-execute/re-execute`,
  },

  // Scheduler endpoints
  SCHEDULERS: {
    LIST: `${WORKFLOW_TRIGGER_API_URL}/schedulers/schedulers`,
    CREATE: `${WORKFLOW_TRIGGER_API_URL}/schedulers/schedulers`,
    GET: (id: string) => `${WORKFLOW_TRIGGER_API_URL}/schedulers/schedulers/${id}`,
    UPDATE: (id: string) => `${WORKFLOW_TRIGGER_API_URL}/schedulers/schedulers/${id}`,
    DELETE: (id: string) => `${WORKFLOW_TRIGGER_API_URL}/schedulers/schedulers/${id}`,
  },
};

// Export default for easier imports
export default {
  API_BASE_URL,
  AUTH_API_URL,
  WORKFLOWS_API_URL,
  MCPS_API_URL,
  CREDENTIALS_API_URL,
  PROVIDERS_API_URL,
  MODELS_API_URL,
  WORKFLOW_EXECUTION_URL,
  MARKETPLACE_URL,
  API_ENDPOINTS,
};
