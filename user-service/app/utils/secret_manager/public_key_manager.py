import hmac
import hashlib


class HMACAPIKeyManager:
    def __init__(self, encryption_key: str):
        """
        Initialize with the user's encryption key from GSM as the private key.

        Args:
            encryption_key (str): The user's encryption key from Google Secret Manager
        """
        self.encryption_key = encryption_key.encode() if isinstance(encryption_key, str) else encryption_key

    def _derive_public_key(self, user_id: str, counter: int) -> str:
        """
        Derives a deterministic HMAC-SHA256 public key using the encryption key, user_id, and counter.

        Args:
            user_id (str): The user ID
            counter (int): The user's API key counter

        Returns:
            str: A deterministic public key (HMAC digest)
        """
        # Create message from user_id and counter for deterministic generation
        message = f"{user_id}:{counter}".encode()
        digest = hmac.new(self.encryption_key, message, hashlib.sha256).hexdigest()
        return digest[:32]  # Return first 32 characters for compactness

    def generate_public_key(self, user_id: str, counter: int) -> str:
        """
        Generates a new public key using the encryption key as private key,
        with the format: ruh-{counter}-{key}-{user_id}

        Args:
            user_id (str): The user ID
            counter (int): The user's API key counter (unique per user)

        Returns:
            str: A unique public key in format ruh-{counter}-{key}-{user_id}
        """
        # Generate the main HMAC key
        main_key = self._derive_public_key(user_id, counter)
        # Create the final format
        return f"ruh-{counter}-{main_key}-{user_id}"

    def validate_public_key(self, public_key: str, user_encryption_key: str) -> bool:
        """
        Validates a public key against the user's encryption key.
        Splits the key to extract components and validates the HMAC part.

        Args:
            public_key (str): The public key to validate in format ruh-{counter}-{key}-{user_id}
            user_encryption_key (str): The user's encryption key from GSM

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            if not public_key or not public_key.startswith("ruh-"):
                return False

            # Remove the "ruh-" prefix
            remaining = public_key[4:]  # Remove "ruh-"

            # Split by "-" to get all parts
            parts = remaining.split("-")
            if len(parts) < 3:  # Must have at least counter, key, and user_id parts
                return False

            # Extract counter (first part)
            counter_str = parts[0]

            # Extract main_key (second part)
            main_key = parts[1]

            # The remaining parts form the user_id (rejoin with hyphens)
            user_id = "-".join(parts[2:])

            # Validate counter is numeric
            try:
                counter = int(counter_str)
            except ValueError:
                return False

            # Validate that main_key looks like a hex digest (32 characters, hex only)
            if len(main_key) != 32 or not all(c in '0123456789abcdef' for c in main_key.lower()):
                return False

            # Recreate the main key using the provided encryption key
            encryption_key_bytes = user_encryption_key.encode() if isinstance(user_encryption_key, str) else user_encryption_key
            message = f"{user_id}:{counter}".encode()
            computed_digest = hmac.new(encryption_key_bytes, message, hashlib.sha256).hexdigest()[:32]

            return hmac.compare_digest(main_key, computed_digest)
        except Exception as e:
            print(f"Error validating public key: {e}")
            return False

    def parse_public_key(self, public_key: str) -> dict:
        """
        Parses a public key to extract user_id, counter, and main_key.

        Args:
            public_key (str): The public key in format ruh-{counter}-{key}-{user_id}

        Returns:
            dict: Dictionary containing user_id, counter, and main_key, or None if invalid
        """
        try:
            if not public_key or not public_key.startswith("ruh-"):
                return None

            # Remove the "ruh-" prefix
            remaining = public_key[4:]  # Remove "ruh-"

            # Split by "-" to get all parts
            parts = remaining.split("-")
            if len(parts) < 3:  # Must have at least counter, key, and user_id parts
                return None

            # Extract counter (first part)
            counter_str = parts[0]

            # Extract main_key (second part)
            main_key = parts[1]

            # The remaining parts form the user_id (rejoin with hyphens)
            user_id = "-".join(parts[2:])

            # Validate counter is numeric
            try:
                counter = int(counter_str)
            except ValueError:
                return None

            # Validate that main_key looks like a hex digest (32 characters, hex only)
            if len(main_key) != 32 or not all(c in '0123456789abcdef' for c in main_key.lower()):
                return None

            return {
                "prefix": "ruh",
                "counter": counter,
                "main_key": main_key,
                "user_id": user_id
            }
        except Exception as e:
            print(f"Error parsing public key: {e}")
            return None


