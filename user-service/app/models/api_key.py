from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey, String, DateTime ,Integer
from sqlalchemy.orm import relationship
from app.models.user import Base

class APIKey(Base):
    __tablename__ = "api-keys"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    # type = Column(Enum(APIKeyTypeEnum), nullable=False)
    # is_default = Column(Boolean, default=False)
    # project = Column(String, nullable=True)
    # status = Column(Enum(APIKeyStatusEnum), nullable=False, default=APIKeyStatusEnum.ACTIVE)
    organization_id = Column(String, nullable=False)
    counter = Column(Integer, default=0)
    is_active = Column(Boolean, default=False)
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship with User
    user = relationship("User", backref="api_keys")

    def __repr__(self):
        return f"<APIKey {self.name}>"