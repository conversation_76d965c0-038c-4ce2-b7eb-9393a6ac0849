"""Remove credential_summary field - using available_nodes directly

Revision ID: 006_remove_credential_summary_field
Revises: 005_add_credential_analysis_fields
Create Date: 2025-08-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_remove_credential_summary_field'
down_revision = '005_add_credential_analysis_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Remove credential_summary field from workflows table
    
    We're now using available_nodes directly for authentication analysis,
    eliminating data duplication and sync issues.
    """
    
    # Remove credential_summary column from workflows table
    op.drop_column('workflows', 'credential_summary')


def downgrade() -> None:
    """
    Re-add credential_summary field to workflows table
    """
    
    # Re-add credential_summary JSONB field
    op.add_column(
        'workflows', 
        sa.Column(
            'credential_summary', 
            postgresql.JSON(astext_type=sa.Text()), 
            nullable=True, 
            default=None,
            comment="Precomputed credential requirements for marketplace authentication"
        )
    )
