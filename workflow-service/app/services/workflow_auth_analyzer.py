"""
Workflow Authentication Analyzer

This module provides functionality to analyze workflows for authentication requirements
and store the analysis results in the available_nodes structure.
Enhanced with optimized credential analysis for marketplace authentication.
"""

from typing import Dict, List, Any, Optional
import json
import logging

# Import the new optimized credential analyzer
from app.services.optimized_credential_analyzer import OptimizedCredentialAnalyzer
from app.models.workflow_builder.credential_analysis import determine_env_credential_status

logger = logging.getLogger(__name__)


class WorkflowAuthenticationAnalyzer:
    """
    Analyzes workflows for authentication requirements including environment keys
    and OAuth details from node configurations.
    """

    def analyze_workflow_authentication(self, workflow_data: Dict) -> Dict[str, Any]:
        """
        Extract authentication requirements from workflow's existing env_keys and oauth_details

        Args:
            workflow_data: Workflow JSON containing start_nodes and available_nodes

        Returns:
            Authentication summary to be stored in available_nodes
        """
        env_keys_required = []
        oauth_providers_required = []
        components_with_auth = []

        # Process all nodes in workflow
        all_nodes = []
        if "start_nodes" in workflow_data:
            all_nodes.extend(workflow_data.get("start_nodes", []))
        if "available_nodes" in workflow_data:
            all_nodes.extend(workflow_data.get("available_nodes", []))

        for node in all_nodes:
            node_env_keys = node.get("env_keys", [])
            oauth_details = node.get("oauth_details")
            component_name = node.get("name", node.get("type", "Unknown Component"))

            # Skip nodes without authentication requirements
            if not node_env_keys and not oauth_details:
                continue

            # Extract environment keys
            if node_env_keys:
                env_keys_required.extend(node_env_keys)

            # Extract OAuth provider
            oauth_provider = None
            if oauth_details and isinstance(oauth_details, dict):
                oauth_provider = oauth_details.get("provider")
                if oauth_provider:
                    oauth_providers_required.append(oauth_provider)

            # Track component with authentication
            component_auth_info = {
                "component_name": component_name,
                "env_keys": node_env_keys,
            }
            if oauth_provider:
                component_auth_info["oauth_provider"] = oauth_provider

            components_with_auth.append(component_auth_info)

        # Remove duplicates
        env_keys_required = list(set(env_keys_required))
        oauth_providers_required = list(set(oauth_providers_required))

        # Calculate totals
        total_auth_items = len(env_keys_required) + len(oauth_providers_required)
        requires_authentication = total_auth_items > 0

        return {
            "requires_authentication": requires_authentication,
            "env_keys_required": env_keys_required,
            "oauth_providers_required": oauth_providers_required,
            "total_auth_items": total_auth_items,
            "components_with_auth": components_with_auth,
            "auth_complexity": self._calculate_complexity(total_auth_items),
            "analyzed_at": "2025-01-22T00:00:00Z",  # Timestamp for cache invalidation
        }

    def _calculate_complexity(self, total_auth_items: int) -> str:
        """
        Calculate authentication complexity based on number of requirements

        Args:
            total_auth_items: Total number of authentication items required

        Returns:
            Complexity level: none, simple, moderate, complex
        """
        if total_auth_items == 0:
            return "none"
        elif total_auth_items <= 2:
            return "simple"
        elif total_auth_items <= 5:
            return "moderate"
        else:
            return "complex"

    def add_auth_summary_to_available_nodes(
        self, available_nodes: List[Dict], auth_summary: Dict
    ) -> List[Dict]:
        """
        Add authentication summary to available_nodes structure

        Args:
            available_nodes: List of available nodes
            auth_summary: Authentication summary to add

        Returns:
            Updated available_nodes with authentication summary
        """
        # Create a copy to avoid modifying the original
        updated_nodes = available_nodes.copy()

        # Add authentication summary as a special node
        auth_node = {"type": "auth_summary", "auth_summary": auth_summary}

        updated_nodes.append(auth_node)
        return updated_nodes

    def get_auth_summary_from_available_nodes(self, available_nodes: List[Dict]) -> Optional[Dict]:
        """
        Extract authentication summary from available_nodes

        Args:
            available_nodes: List of available nodes

        Returns:
            Authentication summary if found, None otherwise
        """
        for node in available_nodes:
            if node.get("type") == "auth_summary" and "auth_summary" in node:
                return node["auth_summary"]
        return None


# Helper functions for easy access
def analyze_and_update_workflow_auth(workflow_data: Dict) -> Dict:
    """
    Enhanced workflow authentication analysis with optimized credential analysis

    This function now performs both:
    1. Legacy analysis for available_nodes (backward compatibility)
    2. New optimized analysis for credential_summary (marketplace optimization)

    Args:
        workflow_data: Complete workflow data

    Returns:
        Updated workflow data with auth summary in available_nodes and credential analysis
    """
    try:
        logger.info("Starting enhanced workflow authentication analysis")

        # 1. Legacy analysis for backward compatibility
        legacy_analyzer = WorkflowAuthenticationAnalyzer()
        auth_summary = legacy_analyzer.analyze_workflow_authentication(workflow_data)

        # Update available_nodes with auth summary (existing behavior)
        available_nodes = workflow_data.get("available_nodes", [])
        updated_available_nodes = legacy_analyzer.add_auth_summary_to_available_nodes(
            available_nodes, auth_summary
        )
        workflow_data["available_nodes"] = updated_available_nodes

        # 2. New optimized analysis for marketplace
        optimized_analyzer = OptimizedCredentialAnalyzer()

        # Validate workflow data first
        validation_errors = optimized_analyzer.validate_workflow_data(workflow_data)
        if validation_errors:
            logger.warning(f"Workflow validation issues: {', '.join(validation_errors)}")
            # Continue with analysis even if there are minor validation issues

        # Perform optimized credential analysis
        credential_summary = optimized_analyzer.analyze_workflow_optimized(workflow_data)

        # Add the optimized analysis results to workflow_data
        # Use mode='json' to properly serialize datetime objects
        workflow_data["credential_summary"] = credential_summary.model_dump(mode="json")
        workflow_data["env_credential_status"] = determine_env_credential_status(
            workflow_data["credential_summary"]
        )

        logger.info(f"Authentication analysis complete:")
        logger.info(
            f"- Legacy auth summary: {len(auth_summary.get('env_keys_required', []))} env keys, {len(auth_summary.get('oauth_providers_required', []))} oauth providers"
        )
        logger.info(
            f"- Optimized analysis: {credential_summary.total_requirements} total requirements, status: {workflow_data['env_credential_status']}"
        )

        return workflow_data

    except Exception as e:
        logger.error(f"Error in workflow authentication analysis: {str(e)}")
        # Return original workflow_data if analysis fails to avoid breaking workflow creation
        return workflow_data


def get_workflow_auth_summary(workflow_data: Dict) -> Optional[Dict]:
    """
    Get authentication summary from workflow's available_nodes

    Args:
        workflow_data: Workflow data containing available_nodes

    Returns:
        Authentication summary if found, None otherwise
    """
    analyzer = WorkflowAuthenticationAnalyzer()
    available_nodes = workflow_data.get("available_nodes", [])
    return analyzer.get_auth_summary_from_available_nodes(available_nodes)
