"""
Utility module for transforming workflow data from sample_workflow.json format
to the expected output format for workflow processing.
"""

from typing import Dict, Any, List
import json
from app.services.workflow_builder.workflow_schema_converter import (
    build_graph_from_workflow,
    compute_levels,
    group_nodes_by_level,
)


def _should_skip_node(node: Dict[str, Any]) -> bool:
    """
    Determine if a node should be skipped during transformation.

    Args:
        node: The node to check

    Returns:
        True if the node should be skipped, False otherwise
    """
    node_data = node.get("data", {})
    original_type = node_data.get("originalType", "")

    # Skip StartNode and other system nodes
    skip_types = ["StartNode", "EndNode"]

    # Skip if it's a system node
    if original_type in skip_types:
        return True

    return False


def _is_tool_node(node: Dict[str, Any], edges: List[Dict[str, Any]]) -> bool:
    """
    Check if a node is connected as a tool to an AgenticAI node.

    Args:
        node: The node to check
        edges: List of edges in the workflow

    Returns:
        True if the node is connected to an AgenticAI node's tools handle
    """
    node_id = node.get("id")

    # Check if this node connects to any AgenticAI node's tools handle
    for edge in edges:
        if edge.get("source") == node_id and edge.get("targetHandle") == "tools":
            return True
    return False


def extract_mcp_and_component_nodes_exact_format(
    workflow_data: Dict[str, Any],
) -> List[Dict[str, Any]]:
    """
    Extract all nodes that are of type 'mcp', 'component', or 'agent' from workflow_data
    following the exact format specification and the EXACT SAME LEVEL-BASED PROCESSING ORDER
    as the transition schema converter.

    This function imports and reuses the existing level computation functions from
    workflow_schema_converter.py to ensure available_nodes are ordered according to
    execution sequence, not just the nodes array order.

    Uses:
        - build_graph_from_workflow() from workflow_schema_converter
        - compute_levels() from workflow_schema_converter
        - group_nodes_by_level() from workflow_schema_converter

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List[Dict[str, Any]] containing all MCP, component, and agent nodes in exact format,
        ordered according to the transition schema execution sequence (level-based)
    """
    print("🔥 EXTRACTING AVAILABLE NODES IN TRANSITION EXECUTION ORDER")
    print("=" * 70)

    if not workflow_data or "nodes" not in workflow_data:
        return []

    nodes = workflow_data.get("nodes", [])
    edges = workflow_data.get("edges", [])

    # Create node lookup for easy access
    node_lookup = {node.get("id"): node for node in nodes}

    # STEP 1: Find start node (same as schema converter)
    print("\n🎯 IDENTIFYING START NODE...")
    start_node_id = None
    for node in nodes:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            print(f"✅ Found start node: {start_node_id}")
            break

    if not start_node_id:
        print("⚠️  No start node found in workflow")

    # STEP 2: Find nodes connected to start node (same as schema converter)
    nodes_connected_to_start = []
    if start_node_id:
        print(f"\n🔗 FINDING NODES CONNECTED TO START NODE...")
        for edge in edges:
            if edge["source"] == start_node_id:
                target_id = edge["target"]
                nodes_connected_to_start.append(target_id)
                print(f"   - {target_id}")
        print(f"✅ Found {len(nodes_connected_to_start)} nodes connected to start")

    # STEP 3: Build graph and compute levels (EXACTLY like schema converter)
    print(f"\n🏗️  BUILDING WORKFLOW GRAPH...")
    graph, edge_map, all_nodes = build_graph_from_workflow(workflow_data)
    print(f"   - Graph nodes: {len(graph)}")
    print(f"   - All nodes: {len(all_nodes)}")

    # Remove start node from graph and all_nodes (same as schema converter)
    if start_node_id:
        if start_node_id in graph:
            del graph[start_node_id]
            print(f"   - Removed start node from graph: {start_node_id}")
        all_nodes.discard(start_node_id)

    # STEP 4: Compute levels using EXACT same logic as schema converter
    print(f"\n📊 COMPUTING NODE LEVELS...")
    levels = compute_levels(graph, all_nodes, nodes_connected_to_start)
    print(f"   - Node levels computed for {len(levels)} nodes")

    # Group nodes by level
    level_groups = group_nodes_by_level(levels)
    print(f"   - Grouped into {len(level_groups)} levels")

    for level in sorted(level_groups.keys()):
        print(f"   - Level {level}: {level_groups[level]}")

    # STEP 5: Process nodes BY LEVEL ORDER (not nodes array order!)
    print(f"\n🔄 PROCESSING NODES BY EXECUTION LEVEL ORDER...")
    print("=" * 50)

    result_nodes = []

    # Process each level in order
    for level in sorted(level_groups.keys()):
        node_ids_at_level = level_groups[level]
        print(f"\n🏗️  PROCESSING LEVEL {level}")
        print(f"   Nodes at this level: {node_ids_at_level}")

        for node_id in node_ids_at_level:
            node = node_lookup.get(node_id)
            if not node:
                print(f"   ❌ ERROR: Node {node_id} not found in lookup")
                continue

            original_type = node.get("data", {}).get("originalType", "unknown")
            node_type = node.get("data", {}).get("type", "unknown")

            print(f"\n   📦 Processing level-{level} node: {node_id}")
            print(f"      Type: {original_type} ({node_type})")

            # Navigate to nodes -> data
            node_data = node.get("data", {})
            transition_id = f"transition-{node.get('id')}"

            try:
                # Apply the same filtering logic as the transition schema converter

                # Skip tool nodes (nodes connected to AgenticAI tools handle)
                if _is_tool_node(node, edges):
                    print(f"      ⏭️  SKIPPED: Tool node (connected to agent tools handle)")
                    continue

                # Skip other system nodes
                if _should_skip_node(node):
                    print(f"      ⏭️  SKIPPED: System node ({original_type})")
                    continue

                # Only process MCP, component, and agent nodes (same logic as schema converter)
                if node_type not in ["mcp", "component", "agent"]:
                    print(f"      ⏭️  SKIPPED: Invalid node type ({node_type})")
                    continue

                print(f"      ✅ PROCESSING: Available node (transition_id: {transition_id})")

            except Exception as e:
                print(f"      ❌ ERROR processing available_node {node_id}: {str(e)}")
                continue

            # Extract definition and create transformed node
            definition = node_data.get("definition", {})

            try:
                if node_type == "component":
                    # Component format: {name, display_name, type, label}
                    transformed_node = {
                        "name": definition.get("name", ""),
                        "display_name": definition.get("display_name", ""),
                        "type": node_type,
                        "transition_id": transition_id,
                        "label": node_data.get("label", ""),
                    }
                    print(f"      📄 COMPONENT: {definition.get('name', 'Unknown')}")

                elif node_type == "mcp":
                    # MCP format: {name, id, type, data: {display_name, input_schema, output_schema}, label}
                    # Include authentication fields for credential analysis
                    mcp_info = definition.get("mcp_info", {})

                    transformed_node = {
                        "name": definition.get("name", ""),
                        "id": mcp_info.get("server_id", ""),
                        "transition_id": transition_id,
                        "type": node_type,
                        "display_name": definition.get("display_name", ""),
                        "label": node_data.get("label", ""),
                        "data": {
                            "input_schema": mcp_info.get("input_schema", {}),
                            "output_schema": mcp_info.get("output_schema", {}),
                        },
                        # Authentication fields for credential analysis
                        "oauth_details": definition.get("oauth_details", {}),
                        "env_credential_status": definition.get("env_credential_status"),
                        "env_keys": definition.get("env_keys", []),
                    }
                    print(
                        f"      🔗 MCP: {definition.get('name', 'Unknown')} (server: {mcp_info.get('server_id', 'Unknown')})"
                    )

                elif node_type == "agent":
                    # Agent format: {name, display_name, type, transition_id, label}
                    transformed_node = {
                        "name": definition.get("name", ""),
                        "display_name": definition.get("display_name", ""),
                        "type": node_type,
                        "transition_id": transition_id,
                        "label": node_data.get("label", ""),
                    }
                    print(f"      🤖 AGENT: {definition.get('name', 'Unknown')}")

                else:
                    print(f"      ❌ UNKNOWN TYPE: {node_type}")
                    continue

                result_nodes.append(transformed_node)
                print(f"      ✅ ADDED: To available_nodes array (position #{len(result_nodes)})")

            except Exception as e:
                print(f"      ❌ ERROR creating available_node for {node_id}: {str(e)}")
                continue

    print(f"\n📊 AVAILABLE NODES EXTRACTION SUMMARY:")
    print(
        f"   - Total nodes processed by level: {sum(len(nodes) for nodes in level_groups.values())}"
    )
    print(f"   - Available nodes extracted: {len(result_nodes)}")
    print(f"   - Extraction order: LEVEL-BASED (matches transition execution order) ✅")

    if result_nodes:
        print(f"   - Final available_nodes sequence (by execution level):")
        for idx, node in enumerate(result_nodes):
            print(
                f"     {idx+1}. {node.get('name', 'Unknown')} ({node.get('type')}) -> {node.get('transition_id')}"
            )

    return result_nodes


# if __name__ == "__main__":
#     schema_path = "sample_workflow.json"
#     with open(schema_path, "r") as f:
#         sample_workflow = json.load(f)

#     nodes = extract_mcp_and_component_nodes_exact_format(sample_workflow)
#     print(nodes)
