"""
Marketplace Authentication Helper

Utility functions to extract authentication requirements from available_nodes
for marketplace display and workflow status determination.

This replaces the complex credential_summary approach with a simpler
direct analysis of available_nodes data.
"""

from typing import List, Dict, Any, Optional


def get_marketplace_auth_requirements(available_nodes: List[Dict]) -> List[Dict]:
    """
    Extract authentication requirements for marketplace display from available_nodes
    
    Args:
        available_nodes: List of workflow nodes with authentication details
        
    Returns:
        List of authentication requirements for marketplace display
    """
    requirements = []
    seen_requirements = set()  # To avoid duplicates
    
    for node in available_nodes:
        node_name = node.get('name', 'Unknown Node')
        
        # Check OAuth requirements
        oauth_details = node.get('oauth_details')
        if oauth_details and isinstance(oauth_details, dict):
            provider = oauth_details.get('provider', 'unknown')
            tool_name = oauth_details.get('tool_name', 'unknown')
            
            # Create unique key to avoid duplicates
            oauth_key = f"oauth:{provider}:{tool_name}"
            if oauth_key not in seen_requirements:
                requirements.append({
                    'type': 'oauth',
                    'provider': provider,
                    'tool_name': tool_name,
                    'description': f'{provider.title()} OAuth authentication for {tool_name}',
                    'node_name': node_name
                })
                seen_requirements.add(oauth_key)
        
        # Check environment key requirements
        env_keys = node.get('env_keys', [])
        for env_key in env_keys:
            if isinstance(env_key, dict):
                # Dictionary format: {'key': 'TAVILY_API_KEY', 'description': '...'}
                key_name = env_key.get('key', '')
                key_description = env_key.get('description', f'Environment variable: {key_name}')
            elif isinstance(env_key, str):
                # String format: 'TAVILY_API_KEY'
                key_name = env_key
                key_description = f'Environment variable: {key_name}'
            else:
                continue
            
            if key_name:
                # Create unique key to avoid duplicates
                env_key_unique = f"api_key:{key_name}"
                if env_key_unique not in seen_requirements:
                    requirements.append({
                        'type': 'api_key',
                        'key': key_name,
                        'description': key_description,
                        'node_name': node_name
                    })
                    seen_requirements.add(env_key_unique)
    
    return requirements


def get_workflow_auth_summary(available_nodes: List[Dict]) -> Dict[str, Any]:
    """
    Get a comprehensive authentication summary for workflow
    
    Args:
        available_nodes: List of workflow nodes with authentication details
        
    Returns:
        Dictionary with authentication summary information
    """
    requirements = get_marketplace_auth_requirements(available_nodes)
    
    # Group by type
    oauth_requirements = [req for req in requirements if req['type'] == 'oauth']
    api_key_requirements = [req for req in requirements if req['type'] == 'api_key']
    
    # Get unique providers
    oauth_providers = list(set(req['provider'] for req in oauth_requirements))
    
    return {
        'total_requirements': len(requirements),
        'oauth_requirements': len(oauth_requirements),
        'api_key_requirements': len(api_key_requirements),
        'oauth_providers': oauth_providers,
        'requirements': requirements,
        'has_authentication': len(requirements) > 0
    }


def determine_auth_status_from_nodes(available_nodes: List[Dict]) -> str:
    """
    Determine authentication status from available_nodes
    
    Args:
        available_nodes: List of workflow nodes with authentication details
        
    Returns:
        'pending_input' if any node requires authentication, 'not_required' otherwise
    """
    if not available_nodes:
        return "not_required"
    
    for node in available_nodes:
        # Check OAuth requirements
        oauth_details = node.get('oauth_details')
        env_status = node.get('env_credential_status', '')
        
        if oauth_details and env_status in ['pending_input', 'not_provided']:
            return 'pending_input'
            
        # Check environment key requirements  
        env_keys = node.get('env_keys', [])
        if env_keys and env_status in ['not_provided', 'pending_input']:
            return 'pending_input'
    
    return 'not_required'


def get_node_auth_requirements(node: Dict) -> List[Dict]:
    """
    Get authentication requirements for a single node
    
    Args:
        node: Single workflow node with authentication details
        
    Returns:
        List of authentication requirements for this node
    """
    return get_marketplace_auth_requirements([node])


def has_authentication_requirements(available_nodes: List[Dict]) -> bool:
    """
    Check if workflow has any authentication requirements
    
    Args:
        available_nodes: List of workflow nodes with authentication details
        
    Returns:
        True if any authentication is required, False otherwise
    """
    requirements = get_marketplace_auth_requirements(available_nodes)
    return len(requirements) > 0


def get_auth_providers_summary(available_nodes: List[Dict]) -> Dict[str, List[str]]:
    """
    Get summary of authentication providers used in workflow
    
    Args:
        available_nodes: List of workflow nodes with authentication details
        
    Returns:
        Dictionary mapping provider types to list of providers
    """
    requirements = get_marketplace_auth_requirements(available_nodes)
    
    oauth_providers = []
    api_key_services = []
    
    for req in requirements:
        if req['type'] == 'oauth':
            provider = req['provider']
            if provider not in oauth_providers:
                oauth_providers.append(provider)
        elif req['type'] == 'api_key':
            service = req.get('key', '').split('_')[0].lower()  # Extract service name from key
            if service and service not in api_key_services:
                api_key_services.append(service)
    
    return {
        'oauth_providers': oauth_providers,
        'api_key_services': api_key_services
    }
