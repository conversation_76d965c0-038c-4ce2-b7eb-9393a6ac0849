{"name": "Untitled Workflow 7", "description": "Untitled_Workflow_7", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": -120, "y": 60}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"MCP_Google_Document_append_document-1754038223238_document_id": {"node_id": "MCP_Google_Document_append_document-1754038223238", "node_name": "Google Document - append_document", "input_name": "document_id", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_Google_Document_append_document-1754038223238_content": {"node_id": "MCP_Google_Document_append_document-1754038223238", "node_name": "Google Document - append_document", "input_name": "content", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 388, "height": 238, "selected": false, "dragging": false, "positionAbsolute": {"x": -120, "y": 60}}, {"id": "MCP_Google_Document_append_document-1754038223238", "type": "WorkflowNode", "position": {"x": 400, "y": 120}, "data": {"label": "Google Document - append_document", "type": "mcp", "originalType": "MCP_Google_Document_append_document", "definition": {"name": "MCP_Google_Document_append_document", "display_name": "Google Document - append_document", "description": "Append content to the end of a Google Document", "category": "file_handling", "icon": "Cloud", "beta": true, "inputs": [{"name": "document_id", "display_name": "Document Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "format", "display_name": "Format", "info": "", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "plain", "options": ["plain", "html", "markdown"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "Upload Document", "display_name": "Upload Document", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_google_document_append_document", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png", "oauth_details": {"provider": "google", "tool_name": "google_document", "is_connected": true}, "mcp_info": {"server_id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "server_path": "", "tool_name": "append_document", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"properties": {"Upload Document": {"type": "string", "description": "user uploads their document", "title": "Upload Document"}}}}}, "config": {"format": "plain"}, "oauthConnectionState": {"isConnected": true, "provider": "google", "connectedAt": "2025-08-01T11:46:03.126Z"}}, "width": 388, "height": 384, "selected": true, "dragging": false, "style": {"opacity": 1}, "positionAbsolute": {"x": 400, "y": 120}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "MCP_Google_Document_append_document-1754038223238", "targetHandle": "document_id", "type": "default", "id": "reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id"}]}, "start_node_data": [{"field": "document_id", "type": "string", "transition_id": "transition-MCP_Google_Document_append_document-1754038223238"}, {"field": "content", "type": "string", "transition_id": "transition-MCP_Google_Document_append_document-1754038223238"}]}