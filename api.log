prat<PERSON><PERSON><PERSON><PERSON>@Pratham-ka-MacBook-Air backend % cd api-gateway 
prathamagarwal@Pratham-ka-MacBook-Air api-gateway % ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Repository already exists at: /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/proto-definitions, removing and cloning fresh copy
Cloning into 'proto-definitions'...
remote: Enumerating objects: 991, done.
remote: Counting objects: 100% (147/147), done.
remote: Compressing objects: 100% (147/147), done.
remote: Total 991 (delta 64), reused 2 (delta 0), pack-reused 844
Receiving objects: 100% (991/991), 244.85 KiB | 7.65 MiB/s, done.
Resolving deltas: 100% (526/526), done.
Successfully cloned fresh copy of repository: https://oauth2:<EMAIL>/ruh.ai/proto-definitions.git dev
Successfully generated gRPC code for user.proto
Successfully fixed imports in generated files for user.proto
Successfully generated gRPC code for admin.proto
Successfully fixed imports in generated files for admin.proto
Successfully generated gRPC code for notification.proto
Successfully fixed imports in generated files for notification.proto
communication.proto:7:1: warning: Import google/protobuf/any.proto is unused.
Successfully generated gRPC code for communication.proto
Successfully fixed imports in generated files for communication.proto
Successfully generated gRPC code for workflow.proto
Successfully fixed imports in generated files for workflow.proto
Successfully generated gRPC code for agent.proto
Successfully fixed imports in generated files for agent.proto
Successfully generated gRPC code for mcp.proto
Successfully fixed imports in generated files for mcp.proto
Successfully generated gRPC code for organisation.proto
Successfully fixed imports in generated files for organisation.proto
Successfully generated gRPC code for authentication.proto
Successfully fixed imports in generated files for authentication.proto
Successfully generated gRPC code for agent_graph.proto
Successfully fixed imports in generated files for agent_graph.proto
Successfully generated gRPC code for google_drive.proto
Successfully fixed imports in generated files for google_drive.proto
Successfully generated gRPC code for analytics.proto
Successfully fixed imports in generated files for analytics.proto
Successfully generated gRPC code for provider.proto
Successfully fixed imports in generated files for provider.proto
payment.proto:6:1: warning: Import google/protobuf/struct.proto is unused.
Successfully generated gRPC code for payment.proto
Successfully fixed imports in generated files for payment.proto
Starting API Gateway...
INFO:     Will watch for changes in these directories: ['/Users/<USER>/Desktop/ruh_ai/backend/api-gateway']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [19904] using StatReload
INFO:     Started server process [19917]
INFO:     Waiting for application startup.
API Gateway initialized
INFO:     Application startup complete.
INFO:     127.0.0.1:59094 - "GET /docs HTTP/1.1" 200 OK
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID register_api_v1_auth_register_post for function register at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID verify_email_otp_api_v1_auth_verify_email_otp_post for function verify_email_otp at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID login_api_v1_auth_login_post for function login at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID google_auth_api_v1_auth_google_login_get for function google_auth at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID google_callback_api_v1_auth_google_callback_get for function google_callback at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID access_token_api_v1_auth_access_token_post for function access_token at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID reset_password_otp_api_v1_auth_reset_password_otp_post for function reset_password_otp at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID update_password_api_v1_auth_update_password_post for function update_password at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID get_current_user_info_api_v1_users_me_get for function get_current_user_info at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID get_all_users_api_v1_users_list_users_get for function get_all_users at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/user_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID admin_login_api_v1_admin_auth_login_post for function admin_login at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/admin_routes.py
  warnings.warn(message, stacklevel=1)
/Users/<USER>/Library/Caches/pypoetry/virtualenvs/api-gateway-5LoMyhnZ-py3.13/lib/python3.13/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID admin_refresh_token_api_v1_admin_auth_access_token_post for function admin_refresh_token at /Users/<USER>/Desktop/ruh_ai/backend/api-gateway/app/api/routers/admin_routes.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:59094 - "GET /api/v1/openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:61285 - "OPTIONS /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 200 OK
[DEBUG] gRPC Client: Sending UpdateWorkflowRequest: id: "9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb"
update_mask {
  paths: "name"
  paths: "description"
  paths: "workflow_data"
  paths: "start_nodes"
}
name: "Untitled Workflow 7"
description: "Untitled_Workflow_7"
workflow_data: "{\"nodes\": [{\"id\": \"start-node\", \"type\": \"WorkflowNode\", \"position\": {\"x\": -120, \"y\": 60}, \"data\": {\"label\": \"Start\", \"type\": \"component\", \"originalType\": \"StartNode\", \"definition\": {\"name\": \"StartNode\", \"display_name\": \"Start\", \"description\": \"The starting point for all workflows. Only nodes connected to this node will be executed.\", \"category\": \"Input/Output\", \"icon\": \"Play\", \"beta\": false, \"inputs\": [], \"outputs\": [{\"name\": \"flow\", \"display_name\": \"Flow\", \"output_type\": \"Any\"}], \"is_valid\": true, \"path\": \"components.io.start_node\"}, \"config\": {\"collected_parameters\": {\"MCP_Google_Document_append_document-1754038223238_document_id\": {\"node_id\": \"MCP_Google_Document_append_document-1754038223238\", \"node_name\": \"Google Document - append_document\", \"input_name\": \"document_id\", \"connected_to_start\": true, \"required\": true, \"input_type\": \"string\", \"options\": null}, \"MCP_Google_Document_append_document-1754038223238_content\": {\"node_id\": \"MCP_Google_Document_append_document-1754038223238\", \"node_name\": \"Google Document - append_document\", \"input_name\": \"content\", \"connected_to_start\": true, \"required\": true, \"input_type\": \"string\", \"options\": null}}}}, \"width\": 388, \"height\": 238, \"selected\": false, \"dragging\": false, \"positionAbsolute\": {\"x\": -120, \"y\": 60}}, {\"id\": \"MCP_Google_Document_append_document-1754038223238\", \"type\": \"WorkflowNode\", \"position\": {\"x\": 400, \"y\": 120}, \"data\": {\"label\": \"Google Document - append_document\", \"type\": \"mcp\", \"originalType\": \"MCP_Google_Document_append_document\", \"definition\": {\"name\": \"MCP_Google_Document_append_document\", \"display_name\": \"Google Document - append_document\", \"description\": \"Append content to the end of a Google Document\", \"category\": \"file_handling\", \"icon\": \"Cloud\", \"beta\": true, \"inputs\": [{\"name\": \"document_id\", \"display_name\": \"Document Id\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": null, \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\"}, {\"name\": \"content\", \"display_name\": \"Content\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": null, \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\"}, {\"name\": \"format\", \"display_name\": \"Format\", \"info\": \"\", \"input_type\": \"dropdown\", \"input_types\": null, \"required\": false, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": \"plain\", \"options\": [\"plain\", \"html\", \"markdown\"], \"visibility_rules\": null, \"visibility_logic\": \"OR\"}], \"outputs\": [{\"name\": \"Upload Document\", \"display_name\": \"Upload Document\", \"output_type\": \"string\"}], \"is_valid\": true, \"path\": \"mcp.mcp_google_document_append_document\", \"type\": \"MCP\", \"env_keys\": [], \"env_credential_status\": \"pending_input\", \"logo\": \"https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png\", \"oauth_details\": {\"provider\": \"google\", \"tool_name\": \"google_document\", \"is_connected\": true}, \"mcp_info\": {\"server_id\": \"0931e5d9-fccc-459b-81a5-c1a251d16c7a\", \"server_path\": \"\", \"tool_name\": \"append_document\", \"input_schema\": {\"properties\": {\"document_id\": {\"title\": \"Document Id\", \"type\": \"string\"}, \"content\": {\"title\": \"Content\", \"type\": \"string\"}, \"format\": {\"anyOf\": [{\"enum\": [\"plain\", \"html\", \"markdown\"], \"type\": \"string\"}, {\"type\": \"null\"}], \"default\": \"plain\", \"title\": \"Format\"}}, \"required\": [\"document_id\", \"content\"], \"title\": \"AppendDocument\", \"type\": \"object\"}, \"output_schema\": {\"properties\": {\"Upload Document\": {\"type\": \"string\", \"description\": \"user uploads their document\", \"title\": \"Upload Document\"}}}}}, \"config\": {\"format\": \"plain\"}, \"oauthConnectionState\": {\"isConnected\": true, \"provider\": \"google\", \"connectedAt\": \"2025-08-01T11:36:25.852Z\"}}, \"width\": 388, \"height\": 384, \"selected\": false, \"dragging\": false, \"style\": {\"opacity\": 1}, \"positionAbsolute\": {\"x\": 400, \"y\": 120}}], \"edges\": [{\"animated\": true, \"style\": {\"strokeWidth\": 2, \"zIndex\": 5}, \"source\": \"start-node\", \"sourceHandle\": \"flow\", \"target\": \"MCP_Google_Document_append_document-1754038223238\", \"targetHandle\": \"document_id\", \"type\": \"default\", \"id\": \"reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id\"}]}"
owner {
  id: "c1454e90-09ac-40f2-bde2-833387d7b645"
}
start_nodes: "{\"field\": \"document_id\", \"type\": \"string\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"
start_nodes: "{\"field\": \"content\", \"type\": \"string\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"

[ERROR] gRPC Client: RpcError in patch_workflow: Workflow with ID 9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb not found
INFO:     127.0.0.1:61285 - "PATCH /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50584 - "OPTIONS /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 200 OK
INFO:     127.0.0.1:50586 - "OPTIONS /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 200 OK
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50584 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50584 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50584 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50584 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50768 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:50768 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:51047 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
[EXCEPTION] 404: Workflow not found
INFO:     127.0.0.1:51047 - "GET /api/v1/workflows/9103dd6a-e265-4fe6-8b31-a6c9fa0db0bb HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:54057 - "OPTIONS /api/v1/workflows?page=1&page_size=9 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54058 - "OPTIONS /api/v1/workflows?page=1&page_size=9 HTTP/1.1" 200 OK
[DEBUG] Listing workflows with filters: category=None, status=None, visibility=None, search=None, tags=None
INFO:     127.0.0.1:54057 - "GET /api/v1/workflows?page=1&page_size=9 HTTP/1.1" 200 OK
[DEBUG] Listing workflows with filters: category=None, status=None, visibility=None, search=None, tags=None
INFO:     127.0.0.1:54057 - "GET /api/v1/workflows?page=1&page_size=9 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54057 - "OPTIONS /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54148 - "OPTIONS /api/v1/workflows?page=1&page_size=50 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54149 - "OPTIONS /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54151 - "OPTIONS /api/v1/workflows?page=1&page_size=50 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54153 - "GET /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
[DEBUG] Listing workflows with filters: category=None, status=None, visibility=None, search=None, tags=None
INFO:     127.0.0.1:54057 - "GET /api/v1/workflows?page=1&page_size=50 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54153 - "GET /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
[DEBUG] Listing workflows with filters: category=None, status=None, visibility=None, search=None, tags=None
INFO:     127.0.0.1:54057 - "GET /api/v1/workflows?page=1&page_size=50 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54057 - "GET /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54057 - "OPTIONS /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
[DEBUG] gRPC Client: Sending UpdateWorkflowRequest: id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
update_mask {
  paths: "name"
  paths: "description"
  paths: "workflow_data"
  paths: "start_nodes"
}
name: "Untitled Workflow 7"
description: "Untitled_Workflow_7"
workflow_data: "{\"nodes\": [{\"id\": \"start-node\", \"type\": \"WorkflowNode\", \"position\": {\"x\": -120, \"y\": 60}, \"data\": {\"label\": \"Start\", \"type\": \"component\", \"originalType\": \"StartNode\", \"definition\": {\"name\": \"StartNode\", \"display_name\": \"Start\", \"description\": \"The starting point for all workflows. Only nodes connected to this node will be executed.\", \"category\": \"Input/Output\", \"icon\": \"Play\", \"beta\": false, \"inputs\": [], \"outputs\": [{\"name\": \"flow\", \"display_name\": \"Flow\", \"output_type\": \"Any\"}], \"is_valid\": true, \"path\": \"components.io.start_node\"}, \"config\": {\"collected_parameters\": {}}}, \"width\": 388, \"height\": 238, \"selected\": false, \"dragging\": false}], \"edges\": [], \"unconnected_nodes\": [{\"id\": \"MCP_Google_Document_append_document-1754038223238\", \"type\": \"WorkflowNode\", \"position\": {\"x\": 400, \"y\": 120}, \"data\": {\"label\": \"Google Document - append_document\", \"type\": \"mcp\", \"originalType\": \"MCP_Google_Document_append_document\", \"definition\": {\"name\": \"MCP_Google_Document_append_document\", \"display_name\": \"Google Document - append_document\", \"description\": \"Append content to the end of a Google Document\", \"category\": \"file_handling\", \"icon\": \"Cloud\", \"beta\": true, \"inputs\": [{\"name\": \"document_id\", \"display_name\": \"Document Id\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": null, \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\"}, {\"name\": \"content\", \"display_name\": \"Content\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": null, \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\"}, {\"name\": \"format\", \"display_name\": \"Format\", \"info\": \"\", \"input_type\": \"dropdown\", \"input_types\": null, \"required\": false, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": \"plain\", \"options\": [\"plain\", \"html\", \"markdown\"], \"visibility_rules\": null, \"visibility_logic\": \"OR\"}], \"outputs\": [{\"name\": \"Upload Document\", \"display_name\": \"Upload Document\", \"output_type\": \"string\"}], \"is_valid\": true, \"path\": \"mcp.mcp_google_document_append_document\", \"type\": \"MCP\", \"env_keys\": [], \"env_credential_status\": \"pending_input\", \"logo\": \"https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png\", \"oauth_details\": {\"provider\": \"google\", \"tool_name\": \"google_document\", \"is_connected\": true}, \"mcp_info\": {\"server_id\": \"0931e5d9-fccc-459b-81a5-c1a251d16c7a\", \"server_path\": \"\", \"tool_name\": \"append_document\", \"input_schema\": {\"properties\": {\"document_id\": {\"title\": \"Document Id\", \"type\": \"string\"}, \"content\": {\"title\": \"Content\", \"type\": \"string\"}, \"format\": {\"anyOf\": [{\"enum\": [\"plain\", \"html\", \"markdown\"], \"type\": \"string\"}, {\"type\": \"null\"}], \"default\": \"plain\", \"title\": \"Format\"}}, \"required\": [\"document_id\", \"content\"], \"title\": \"AppendDocument\", \"type\": \"object\"}, \"output_schema\": {\"properties\": {\"Upload Document\": {\"type\": \"string\", \"description\": \"user uploads their document\", \"title\": \"Upload Document\"}}}}}, \"config\": {\"format\": \"plain\"}, \"oauthConnectionState\": {\"isConnected\": true, \"provider\": \"google\", \"connectedAt\": \"2025-08-01T08:59:01.665Z\"}}, \"width\": 388, \"height\": 384, \"selected\": false, \"dragging\": false, \"style\": {\"opacity\": 0.5}}]}"
owner {
  id: "c1454e90-09ac-40f2-bde2-833387d7b645"
}

[DEBUG] gRPC Client: Received response from UpdateWorkflow: success: true
message: "Workflow Untitled Workflow 7 updated successfully (is_updated flag set to true - create new version when ready)"

INFO:     127.0.0.1:54057 - "PATCH /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54638 - "OPTIONS /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54638 - "GET /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54638 - "OPTIONS /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
[DEBUG] gRPC Client: Sending UpdateWorkflowRequest: id: "9a6cd844-d5d0-4211-8a7c-88bd539267c3"
update_mask {
  paths: "name"
  paths: "description"
  paths: "workflow_data"
  paths: "start_nodes"
}
name: "Untitled Workflow 7"
description: "Untitled_Workflow_7"
workflow_data: "{\"nodes\": [{\"id\": \"start-node\", \"type\": \"WorkflowNode\", \"position\": {\"x\": -120, \"y\": 60}, \"data\": {\"label\": \"Start\", \"type\": \"component\", \"originalType\": \"StartNode\", \"definition\": {\"name\": \"StartNode\", \"display_name\": \"Start\", \"description\": \"The starting point for all workflows. Only nodes connected to this node will be executed.\", \"category\": \"Input/Output\", \"icon\": \"Play\", \"beta\": false, \"inputs\": [], \"outputs\": [{\"name\": \"flow\", \"display_name\": \"Flow\", \"output_type\": \"Any\"}], \"is_valid\": true, \"path\": \"components.io.start_node\"}, \"config\": {\"collected_parameters\": {\"MCP_Google_Document_append_document-1754038223238_document_id\": {\"node_id\": \"MCP_Google_Document_append_document-1754038223238\", \"node_name\": \"Google Document - append_document\", \"input_name\": \"document_id\", \"connected_to_start\": true, \"required\": true, \"input_type\": \"string\", \"options\": null}, \"MCP_Google_Document_append_document-1754038223238_content\": {\"node_id\": \"MCP_Google_Document_append_document-1754038223238\", \"node_name\": \"Google Document - append_document\", \"input_name\": \"content\", \"connected_to_start\": true, \"required\": true, \"input_type\": \"string\", \"options\": null}}}}, \"width\": 388, \"height\": 238, \"selected\": false, \"dragging\": false, \"positionAbsolute\": {\"x\": -120, \"y\": 60}}, {\"id\": \"MCP_Google_Document_append_document-1754038223238\", \"type\": \"WorkflowNode\", \"position\": {\"x\": 400, \"y\": 120}, \"data\": {\"label\": \"Google Document - append_document\", \"type\": \"mcp\", \"originalType\": \"MCP_Google_Document_append_document\", \"definition\": {\"name\": \"MCP_Google_Document_append_document\", \"display_name\": \"Google Document - append_document\", \"description\": \"Append content to the end of a Google Document\", \"category\": \"file_handling\", \"icon\": \"Cloud\", \"beta\": true, \"inputs\": [{\"name\": \"document_id\", \"display_name\": \"Document Id\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": [\"string\", \"Any\"], \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\", \"validation\": {}}, {\"name\": \"content\", \"display_name\": \"Content\", \"info\": \"\", \"input_type\": \"string\", \"input_types\": [\"string\", \"Any\"], \"required\": true, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": null, \"options\": null, \"visibility_rules\": null, \"visibility_logic\": \"OR\", \"validation\": {}}, {\"name\": \"format\", \"display_name\": \"Format\", \"info\": \"\", \"input_type\": \"dropdown\", \"input_types\": [\"dropdown\", \"Any\"], \"required\": false, \"is_handle\": true, \"is_list\": false, \"real_time_refresh\": false, \"advanced\": false, \"value\": \"plain\", \"options\": [\"plain\", \"html\", \"markdown\"], \"visibility_rules\": null, \"visibility_logic\": \"OR\", \"validation\": {}}], \"outputs\": [{\"name\": \"Upload Document\", \"display_name\": \"Upload Document\", \"output_type\": \"string\"}], \"is_valid\": true, \"path\": \"mcp.mcp_google_document_append_document\", \"type\": \"MCP\", \"env_keys\": [], \"env_credential_status\": \"pending_input\", \"logo\": \"https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/**********-Google-Docs-logo.png\", \"oauth_details\": {\"provider\": \"google\", \"tool_name\": \"google_document\", \"is_connected\": true}, \"mcp_info\": {\"server_id\": \"0931e5d9-fccc-459b-81a5-c1a251d16c7a\", \"server_path\": \"\", \"tool_name\": \"append_document\", \"input_schema\": {\"properties\": {\"document_id\": {\"title\": \"Document Id\", \"type\": \"string\"}, \"content\": {\"title\": \"Content\", \"type\": \"string\"}, \"format\": {\"anyOf\": [{\"enum\": [\"plain\", \"html\", \"markdown\"], \"type\": \"string\"}, {\"type\": \"null\"}], \"default\": \"plain\", \"title\": \"Format\"}}, \"required\": [\"document_id\", \"content\"], \"title\": \"AppendDocument\", \"type\": \"object\"}, \"output_schema\": {\"properties\": {\"Upload Document\": {\"type\": \"string\", \"description\": \"user uploads their document\", \"title\": \"Upload Document\"}}}}}, \"config\": {\"format\": \"plain\"}, \"oauthConnectionState\": {\"isConnected\": true, \"provider\": \"google\", \"connectedAt\": \"2025-08-01T11:46:03.126Z\"}}, \"width\": 388, \"height\": 384, \"selected\": true, \"dragging\": false, \"style\": {\"opacity\": 1}, \"positionAbsolute\": {\"x\": 400, \"y\": 120}}], \"edges\": [{\"animated\": true, \"style\": {\"strokeWidth\": 2, \"zIndex\": 5}, \"source\": \"start-node\", \"sourceHandle\": \"flow\", \"target\": \"MCP_Google_Document_append_document-1754038223238\", \"targetHandle\": \"document_id\", \"type\": \"default\", \"id\": \"reactflow__edge-start-nodeflow-MCP_Google_Document_append_document-1754038223238document_id\"}]}"
owner {
  id: "c1454e90-09ac-40f2-bde2-833387d7b645"
}
start_nodes: "{\"field\": \"document_id\", \"type\": \"string\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"
start_nodes: "{\"field\": \"content\", \"type\": \"string\", \"transition_id\": \"transition-MCP_Google_Document_append_document-1754038223238\"}"

[DEBUG] gRPC Client: Received response from UpdateWorkflow: success: true
message: "Workflow Untitled Workflow 7 updated successfully (is_updated flag set to true - create new version when ready)"

INFO:     127.0.0.1:54671 - "PATCH /api/v1/workflows/9a6cd844-d5d0-4211-8a7c-88bd539267c3 HTTP/1.1" 200 OK
