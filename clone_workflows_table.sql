-- Clone Workflows Table with Sample Data (10 rows)
-- This script creates a clone of the workflows table with only 10 rows for testing/development
-- Based on the current schema including marketplace authentication fields

-- Drop the clone table if it exists
DROP TABLE IF EXISTS workflows_clone CASCADE;

-- Create the clone table with the same structure as the original workflows table
CREATE TABLE workflows_clone (
    id VARCHAR PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    image_url VARCHAR,
    workflow_url VARCHAR NOT NULL,
    builder_url VARCHAR NOT NULL,
    start_nodes JSON NOT NULL DEFAULT '[]',
    available_nodes JSON NOT NULL DEFAULT '[]',
    owner_id VARCHAR NOT NULL,
    owner_type VARCHAR NOT NULL,
    current_version_id VARCHAR,
    is_imported BOOLEAN DEFAULT FALSE,
    workflow_template_id VARCHAR,
    template_owner_id VARCHAR,
    source_version_id VARCHAR,
    is_customizable BOOLEAN DEFAULT TRUE,
    is_updated B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    user_ids VARCHAR[],
    visibility VARCHAR NOT NULL DEFAULT 'PRIVATE',
    status VARCHAR NOT NULL DEFAULT 'ACTIVE',
    category VARCHAR,
    tags JSON DEFAULT '[]',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Marketplace Authentication Fields (added in migration 005)
    credential_summary JSON,
    env_credential_status VARCHAR(20) DEFAULT 'not_required'
);

-- Create indexes for performance (matching the original table)
CREATE INDEX idx_workflows_clone_owner_id ON workflows_clone(owner_id);
CREATE INDEX idx_workflows_clone_visibility ON workflows_clone(visibility);
CREATE INDEX idx_workflows_clone_status ON workflows_clone(status);
CREATE INDEX idx_workflows_clone_created_at ON workflows_clone(created_at);
CREATE INDEX idx_workflows_clone_env_credential_status ON workflows_clone(env_credential_status);
CREATE INDEX idx_workflows_clone_credential_summary ON workflows_clone USING GIN(credential_summary);

-- Insert 10 sample workflows with diverse data for testing
INSERT INTO workflows_clone (
    id, name, description, image_url, workflow_url, builder_url, 
    start_nodes, available_nodes, owner_id, owner_type, 
    is_imported, workflow_template_id, template_owner_id, 
    is_customizable, is_updated, user_ids, visibility, status, 
    category, tags, created_at, updated_at,
    credential_summary, env_credential_status
) VALUES 
-- 1. Simple workflow with OpenAI integration
(
    'wf-001-openai-simple',
    'OpenAI Content Generator',
    'Generate content using OpenAI GPT models',
    'https://example.com/images/openai-workflow.png',
    'https://storage.googleapis.com/workflows/openai-content-gen.json',
    'https://storage.googleapis.com/builders/openai-content-gen-builder.json',
    '["input_prompt"]',
    '[{"id": "openai_chat", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential", "required": true}}}]',
    'user-001',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-001"]',
    'PUBLIC',
    'ACTIVE',
    'AI',
    '["openai", "content-generation", "ai"]',
    '2025-01-01 10:00:00',
    '2025-01-01 10:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 3,
        "by_provider": {
            "openai": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["openai_api_key"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "openai_api_key",
                "is_required": true,
                "component_count": 1,
                "description": "OpenAI API key for AI components",
                "provider_name": "openai"
            }
        ]
    }',
    'pending_input'
),

-- 2. Complex workflow with multiple providers
(
    'wf-002-multi-provider',
    'Multi-Provider Integration Workflow',
    'Workflow integrating OpenAI, GitHub, and Google services',
    'https://example.com/images/multi-provider.png',
    'https://storage.googleapis.com/workflows/multi-provider.json',
    'https://storage.googleapis.com/builders/multi-provider-builder.json',
    '["data_input"]',
    '[
        {"id": "openai_node", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "github_node", "type": "api_request", "inputs": {"github_token": {"input_type": "credential"}}},
        {"id": "google_node", "type": "oauth_component", "inputs": {"oauth_token": {"input_type": "oauth"}}}
    ]',
    'user-002',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-002"]',
    'PUBLIC',
    'ACTIVE',
    'INTEGRATION',
    '["openai", "github", "google", "integration"]',
    '2025-01-02 11:00:00',
    '2025-01-02 11:00:00',
    '{
        "total_requirements": 3,
        "estimated_setup_time": 18,
        "by_provider": {
            "openai": {"count": 1, "types": ["api_key"], "required": true, "fields": ["openai_api_key"]},
            "github": {"count": 1, "types": ["api_key"], "required": true, "fields": ["github_token"]},
            "google": {"count": 1, "types": ["oauth"], "required": true, "fields": ["google_oauth_token"]}
        },
        "credential_requirements": [
            {"credential_type": "api_key", "field_name": "openai_api_key", "is_required": true, "component_count": 1, "provider_name": "openai"},
            {"credential_type": "api_key", "field_name": "github_token", "is_required": true, "component_count": 1, "provider_name": "github"},
            {"credential_type": "oauth", "field_name": "google_oauth_token", "is_required": true, "component_count": 1, "provider_name": "google"}
        ]
    }',
    'pending_input'
),

-- 3. No authentication required workflow
(
    'wf-003-no-auth',
    'Data Processing Pipeline',
    'Simple data processing without external API calls',
    null,
    'https://storage.googleapis.com/workflows/data-processing.json',
    'https://storage.googleapis.com/builders/data-processing-builder.json',
    '["csv_input"]',
    '[
        {"id": "data_filter", "type": "data_component"},
        {"id": "data_transform", "type": "transform_component"},
        {"id": "output_csv", "type": "output_component"}
    ]',
    'user-003',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-003"]',
    'PUBLIC',
    'ACTIVE',
    'DATA',
    '["data-processing", "csv", "transform"]',
    '2025-01-03 12:00:00',
    '2025-01-03 12:00:00',
    '{
        "total_requirements": 0,
        "estimated_setup_time": 0,
        "by_provider": {},
        "credential_requirements": []
    }',
    'not_required'
),

-- 4. Private workflow with Slack integration
(
    'wf-004-slack-private',
    'Slack Notification System',
    'Send automated notifications to Slack channels',
    'https://example.com/images/slack-workflow.png',
    'https://storage.googleapis.com/workflows/slack-notifications.json',
    'https://storage.googleapis.com/builders/slack-notifications-builder.json',
    '["trigger_event"]',
    '[
        {"id": "slack_webhook", "type": "webhook_component", "inputs": {"webhook_url": {"input_type": "credential", "required": true}}},
        {"id": "message_formatter", "type": "text_component"}
    ]',
    'user-004',
    'USER',
    false,
    null,
    null,
    true,
    true,
    '["user-004", "user-005"]',
    'PRIVATE',
    'ACTIVE',
    'COMMUNICATION',
    '["slack", "notifications", "webhook"]',
    '2025-01-04 13:00:00',
    '2025-01-04 14:30:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 5,
        "by_provider": {
            "slack": {
                "count": 1,
                "types": ["webhook"],
                "required": true,
                "fields": ["slack_webhook_url"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "webhook",
                "field_name": "slack_webhook_url",
                "is_required": true,
                "component_count": 1,
                "description": "Slack webhook URL for notifications",
                "provider_name": "slack"
            }
        ]
    }',
    'pending_input'
),

-- 5. GitHub repository analyzer
(
    'wf-005-github-analyzer',
    'GitHub Repository Analyzer',
    'Analyze GitHub repositories for code quality and metrics',
    'https://example.com/images/github-analyzer.png',
    'https://storage.googleapis.com/workflows/github-analyzer.json',
    'https://storage.googleapis.com/builders/github-analyzer-builder.json',
    '["repo_url"]',
    '[
        {"id": "github_api", "type": "api_request", "inputs": {"github_token": {"input_type": "credential", "required": true}}},
        {"id": "code_analyzer", "type": "analysis_component"},
        {"id": "report_generator", "type": "report_component"}
    ]',
    'user-005',
    'ORGANIZATION',
    false,
    null,
    null,
    true,
    false,
    '["user-005", "user-006", "user-007"]',
    'PUBLIC',
    'ACTIVE',
    'DEVELOPMENT',
    '["github", "code-analysis", "repository"]',
    '2025-01-05 14:00:00',
    '2025-01-05 14:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 8,
        "by_provider": {
            "github": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["github_token"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "github_token",
                "is_required": true,
                "component_count": 1,
                "description": "GitHub Personal Access Token",
                "provider_name": "github"
            }
        ]
    }',
    'pending_input'
),

-- 6. Email automation workflow
(
    'wf-006-email-automation',
    'Email Marketing Automation',
    'Automated email campaigns with personalization',
    'https://example.com/images/email-automation.png',
    'https://storage.googleapis.com/workflows/email-automation.json',
    'https://storage.googleapis.com/builders/email-automation-builder.json',
    '["contact_list"]',
    '[
        {"id": "email_service", "type": "email_component", "inputs": {"smtp_password": {"input_type": "password", "required": true}}},
        {"id": "template_engine", "type": "template_component"},
        {"id": "scheduler", "type": "schedule_component"}
    ]',
    'user-006',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-006"]',
    'PRIVATE',
    'ACTIVE',
    'MARKETING',
    '["email", "automation", "marketing"]',
    '2025-01-06 15:00:00',
    '2025-01-06 15:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 5,
        "by_provider": {
            "email": {
                "count": 1,
                "types": ["password"],
                "required": true,
                "fields": ["smtp_password"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "password",
                "field_name": "smtp_password",
                "is_required": true,
                "component_count": 1,
                "description": "SMTP server password for email sending",
                "provider_name": "email"
            }
        ]
    }',
    'pending_input'
),

-- 7. Imported workflow from template
(
    'wf-007-imported-template',
    'Customer Support Chatbot',
    'AI-powered customer support chatbot template',
    'https://example.com/images/chatbot-template.png',
    'https://storage.googleapis.com/workflows/chatbot-support.json',
    'https://storage.googleapis.com/builders/chatbot-support-builder.json',
    '["user_message"]',
    '[
        {"id": "openai_chat", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "knowledge_base", "type": "database_component"},
        {"id": "response_formatter", "type": "text_component"}
    ]',
    'user-007',
    'USER',
    true,
    'template-chatbot-001',
    'template-owner-001',
    true,
    false,
    '["user-007"]',
    'PUBLIC',
    'ACTIVE',
    'CUSTOMER_SERVICE',
    '["chatbot", "ai", "customer-support", "template"]',
    '2025-01-07 16:00:00',
    '2025-01-07 16:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 3,
        "by_provider": {
            "openai": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["openai_api_key"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "openai_api_key",
                "is_required": true,
                "component_count": 1,
                "description": "OpenAI API key for chatbot responses",
                "provider_name": "openai"
            }
        ]
    }',
    'pending_input'
),

-- 8. Inactive workflow for testing
(
    'wf-008-inactive-test',
    'Inactive Test Workflow',
    'This workflow is inactive for testing purposes',
    null,
    'https://storage.googleapis.com/workflows/inactive-test.json',
    'https://storage.googleapis.com/builders/inactive-test-builder.json',
    '[]',
    '[]',
    'user-008',
    'USER',
    false,
    null,
    null,
    false,
    false,
    '["user-008"]',
    'PRIVATE',
    'INACTIVE',
    null,
    '["test", "inactive"]',
    '2025-01-08 17:00:00',
    '2025-01-08 17:00:00',
    '{
        "total_requirements": 0,
        "estimated_setup_time": 0,
        "by_provider": {},
        "credential_requirements": []
    }',
    'not_required'
),

-- 9. Complex workflow with multiple OpenAI calls
(
    'wf-009-complex-openai',
    'Advanced AI Content Pipeline',
    'Multi-stage AI content generation and refinement pipeline',
    'https://example.com/images/ai-pipeline.png',
    'https://storage.googleapis.com/workflows/ai-content-pipeline.json',
    'https://storage.googleapis.com/builders/ai-content-pipeline-builder.json',
    '["content_brief"]',
    '[
        {"id": "openai_draft", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "openai_review", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "openai_final", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "quality_check", "type": "validation_component"}
    ]',
    'user-009',
    'ORGANIZATION',
    false,
    null,
    null,
    true,
    true,
    '["user-009", "user-010"]',
    'PUBLIC',
    'ACTIVE',
    'AI',
    '["openai", "content-pipeline", "ai", "advanced"]',
    '2025-01-09 18:00:00',
    '2025-01-09 19:30:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 3,
        "by_provider": {
            "openai": {
                "count": 3,
                "types": ["api_key"],
                "required": true,
                "fields": ["openai_api_key"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "openai_api_key",
                "is_required": true,
                "component_count": 3,
                "description": "OpenAI API key for multiple AI components",
                "provider_name": "openai"
            }
        ]
    }',
    'pending_input'
),

-- 10. Discord bot workflow
(
    'wf-010-discord-bot',
    'Discord Community Bot',
    'Automated Discord bot for community management',
    'https://example.com/images/discord-bot.png',
    'https://storage.googleapis.com/workflows/discord-bot.json',
    'https://storage.googleapis.com/builders/discord-bot-builder.json',
    '["discord_event"]',
    '[
        {"id": "discord_api", "type": "api_request", "inputs": {"discord_token": {"input_type": "credential", "required": true}}},
        {"id": "command_processor", "type": "logic_component"},
        {"id": "response_handler", "type": "response_component"}
    ]',
    'user-010',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-010"]',
    'PUBLIC',
    'ACTIVE',
    'COMMUNITY',
    '["discord", "bot", "community", "automation"]',
    '2025-01-10 19:00:00',
    '2025-01-10 19:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 10,
        "by_provider": {
            "discord": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["discord_token"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "discord_token",
                "is_required": true,
                "component_count": 1,
                "description": "Discord bot token for API access",
                "provider_name": "discord"
            }
        ]
    }',
    'pending_input'
),

-- 5. GitHub repository analyzer
(
    'wf-005-github-analyzer',
    'GitHub Repository Analyzer',
    'Analyze GitHub repositories for code quality and metrics',
    'https://example.com/images/github-analyzer.png',
    'https://storage.googleapis.com/workflows/github-analyzer.json',
    'https://storage.googleapis.com/builders/github-analyzer-builder.json',
    '["repo_url"]',
    '[
        {"id": "github_api", "type": "api_request", "inputs": {"github_token": {"input_type": "credential", "required": true}}},
        {"id": "code_analyzer", "type": "analysis_component"},
        {"id": "report_generator", "type": "report_component"}
    ]',
    'user-005',
    'ORGANIZATION',
    false,
    null,
    null,
    true,
    false,
    '["user-005", "user-006", "user-007"]',
    'PUBLIC',
    'ACTIVE',
    'DEVELOPMENT',
    '["github", "code-analysis", "repository"]',
    '2025-01-05 14:00:00',
    '2025-01-05 14:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 8,
        "by_provider": {
            "github": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["github_token"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "github_token",
                "is_required": true,
                "component_count": 1,
                "description": "GitHub Personal Access Token",
                "provider_name": "github"
            }
        ]
    }',
    'pending_input'
),

-- 6. Email automation workflow
(
    'wf-006-email-automation',
    'Email Marketing Automation',
    'Automated email campaigns with personalization',
    'https://example.com/images/email-automation.png',
    'https://storage.googleapis.com/workflows/email-automation.json',
    'https://storage.googleapis.com/builders/email-automation-builder.json',
    '["contact_list"]',
    '[
        {"id": "email_service", "type": "email_component", "inputs": {"smtp_password": {"input_type": "password", "required": true}}},
        {"id": "template_engine", "type": "template_component"},
        {"id": "scheduler", "type": "schedule_component"}
    ]',
    'user-006',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-006"]',
    'PRIVATE',
    'ACTIVE',
    'MARKETING',
    '["email", "automation", "marketing"]',
    '2025-01-06 15:00:00',
    '2025-01-06 15:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 5,
        "by_provider": {
            "email": {
                "count": 1,
                "types": ["password"],
                "required": true,
                "fields": ["smtp_password"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "password",
                "field_name": "smtp_password",
                "is_required": true,
                "component_count": 1,
                "description": "SMTP server password for email sending",
                "provider_name": "email"
            }
        ]
    }',
    'pending_input'
),

-- 7. Imported workflow from template
(
    'wf-007-imported-template',
    'Customer Support Chatbot',
    'AI-powered customer support chatbot template',
    'https://example.com/images/chatbot-template.png',
    'https://storage.googleapis.com/workflows/chatbot-support.json',
    'https://storage.googleapis.com/builders/chatbot-support-builder.json',
    '["user_message"]',
    '[
        {"id": "openai_chat", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "knowledge_base", "type": "database_component"},
        {"id": "response_formatter", "type": "text_component"}
    ]',
    'user-007',
    'USER',
    true,
    'template-chatbot-001',
    'template-owner-001',
    true,
    false,
    '["user-007"]',
    'PUBLIC',
    'ACTIVE',
    'CUSTOMER_SERVICE',
    '["chatbot", "ai", "customer-support", "template"]',
    '2025-01-07 16:00:00',
    '2025-01-07 16:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 3,
        "by_provider": {
            "openai": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["openai_api_key"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "openai_api_key",
                "is_required": true,
                "component_count": 1,
                "description": "OpenAI API key for chatbot responses",
                "provider_name": "openai"
            }
        ]
    }',
    'pending_input'
),

-- 8. Inactive workflow for testing
(
    'wf-008-inactive-test',
    'Inactive Test Workflow',
    'This workflow is inactive for testing purposes',
    null,
    'https://storage.googleapis.com/workflows/inactive-test.json',
    'https://storage.googleapis.com/builders/inactive-test-builder.json',
    '[]',
    '[]',
    'user-008',
    'USER',
    false,
    null,
    null,
    false,
    false,
    '["user-008"]',
    'PRIVATE',
    'INACTIVE',
    null,
    '["test", "inactive"]',
    '2025-01-08 17:00:00',
    '2025-01-08 17:00:00',
    '{
        "total_requirements": 0,
        "estimated_setup_time": 0,
        "by_provider": {},
        "credential_requirements": []
    }',
    'not_required'
),

-- 9. Complex workflow with multiple OpenAI calls
(
    'wf-009-complex-openai',
    'Advanced AI Content Pipeline',
    'Multi-stage AI content generation and refinement pipeline',
    'https://example.com/images/ai-pipeline.png',
    'https://storage.googleapis.com/workflows/ai-content-pipeline.json',
    'https://storage.googleapis.com/builders/ai-content-pipeline-builder.json',
    '["content_brief"]',
    '[
        {"id": "openai_draft", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "openai_review", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "openai_final", "type": "ai_component", "inputs": {"api_key": {"input_type": "credential"}}},
        {"id": "quality_check", "type": "validation_component"}
    ]',
    'user-009',
    'ORGANIZATION',
    false,
    null,
    null,
    true,
    true,
    '["user-009", "user-010"]',
    'PUBLIC',
    'ACTIVE',
    'AI',
    '["openai", "content-pipeline", "ai", "advanced"]',
    '2025-01-09 18:00:00',
    '2025-01-09 19:30:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 3,
        "by_provider": {
            "openai": {
                "count": 3,
                "types": ["api_key"],
                "required": true,
                "fields": ["openai_api_key"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "openai_api_key",
                "is_required": true,
                "component_count": 3,
                "description": "OpenAI API key for multiple AI components",
                "provider_name": "openai"
            }
        ]
    }',
    'pending_input'
),

-- 10. Discord bot workflow
(
    'wf-010-discord-bot',
    'Discord Community Bot',
    'Automated Discord bot for community management',
    'https://example.com/images/discord-bot.png',
    'https://storage.googleapis.com/workflows/discord-bot.json',
    'https://storage.googleapis.com/builders/discord-bot-builder.json',
    '["discord_event"]',
    '[
        {"id": "discord_api", "type": "api_request", "inputs": {"discord_token": {"input_type": "credential", "required": true}}},
        {"id": "command_processor", "type": "logic_component"},
        {"id": "response_handler", "type": "response_component"}
    ]',
    'user-010',
    'USER',
    false,
    null,
    null,
    true,
    false,
    '["user-010"]',
    'PUBLIC',
    'ACTIVE',
    'COMMUNITY',
    '["discord", "bot", "community", "automation"]',
    '2025-01-10 19:00:00',
    '2025-01-10 19:00:00',
    '{
        "total_requirements": 1,
        "estimated_setup_time": 10,
        "by_provider": {
            "discord": {
                "count": 1,
                "types": ["api_key"],
                "required": true,
                "fields": ["discord_token"]
            }
        },
        "credential_requirements": [
            {
                "credential_type": "api_key",
                "field_name": "discord_token",
                "is_required": true,
                "component_count": 1,
                "description": "Discord bot token for API access",
                "provider_name": "discord"
            }
        ]
    }',
    'pending_input'
);

-- Verification queries to check the cloned data
-- Count total rows
SELECT COUNT(*) as total_workflows FROM workflows_clone;

-- Check credential status distribution
SELECT env_credential_status, COUNT(*) as count
FROM workflows_clone
GROUP BY env_credential_status;

-- Check visibility distribution
SELECT visibility, COUNT(*) as count
FROM workflows_clone
GROUP BY visibility;

-- Check workflows with credential requirements
SELECT
    id,
    name,
    env_credential_status,
    JSON_EXTRACT(credential_summary, '$.total_requirements') as total_requirements,
    JSON_EXTRACT(credential_summary, '$.estimated_setup_time') as setup_time_minutes
FROM workflows_clone
WHERE credential_summary IS NOT NULL
ORDER BY JSON_EXTRACT(credential_summary, '$.total_requirements') DESC;

-- Check provider distribution
SELECT
    id,
    name,
    JSON_KEYS(JSON_EXTRACT(credential_summary, '$.by_provider')) as required_providers
FROM workflows_clone
WHERE JSON_EXTRACT(credential_summary, '$.total_requirements') > 0;

-- Sample the data
SELECT
    id,
    name,
    owner_type,
    visibility,
    status,
    env_credential_status,
    created_at
FROM workflows_clone
ORDER BY created_at;

-- Success message
SELECT 'Successfully created workflows_clone table with 10 sample workflows!' as status;
